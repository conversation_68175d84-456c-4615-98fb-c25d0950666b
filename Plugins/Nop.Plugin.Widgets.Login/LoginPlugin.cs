using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nop.Plugin.Widgets.Login.Components;
using Nop.Services.Cms;
using Nop.Services.Plugins;
using Nop.Web.Framework.Infrastructure;

namespace Nop.Plugin.Widgets.Login;

public class LoginPlugin : BasePlugin, IWidgetPlugin
{
    public bool HideInWidgetList { get; }
    public Task<IList<string>> GetWidgetZonesAsync()
    {
        return Task.FromResult<IList<string>>(new List<string>
        {
            PublicWidgetZones.HeadHtmlTag
        });
    }

    public Type GetWidgetViewComponent(string widgetZone)
    {
        return typeof(LoginWidget);
    }
}