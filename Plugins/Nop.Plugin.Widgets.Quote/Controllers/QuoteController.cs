using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using LogErrorsToAutotask.NETCore.Attributes;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Nop.Core;
using Nop.Core.Domain.Discounts;
using Nop.Core.Domain.Messages;
using Nop.Plugin.ExternalAuth.MultiSSO.Models;
using Nop.Plugin.Misc.EdunetCore.Common;
using Nop.Plugin.Misc.EdunetCore.Services;
using Nop.Plugin.Widgets.Quote.Models.EmailTemplates;
using Nop.Plugin.Widgets.Quote.Models.QuoteSelection;
using Nop.Plugin.Widgets.Quote.Services;
using Nop.Services.Catalog;
using Nop.Services.Customers;
using Nop.Services.Discounts;
using Nop.Services.Messages;
using Nop.Services.Orders;
using Nop.Web.Framework.Controllers;
using Nop.Web.Models.ShoppingCart;

namespace Nop.Plugin.Widgets.Quote.Controllers;

public class QuoteController(
    IWorkContext workContext,
    INotificationService notificationService,
    ICustomerAttributeService customerAttributeService,
    ICustomerAttributeParser customerAttributeParser,
    IQueuedEmailService queuedEmailService,
    IEmailAccountService emailAccountService,
    IStoreContext storeContext,
    IShoppingCartService shoppingCartService,
    IProductService productService,
    IQuoteSelectionService quoteSelectionService,
    ISharedDatafeedApiClient sharedDatafeedApiClient,
    IDiscountService discountService,
    ICustomerService customerService)
    : BaseController
{
    [HttpPost]
    [LogErrorsToAutotask("eShop.SendQuote")]
    public async Task<IActionResult> SendQuoteAsync(
        [FromForm] string quoteTitle,
        [FromForm] string[] emailList,
        [FromForm] string cartItems,
        [FromForm] string appliedDiscountsJson,
        uint currentlySelectedSchoolIdx)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return BadRequest("Invalid user");
        }

        // Existing school validation
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));
        Debug.Assert(schoolsAttribute != null, nameof(schoolsAttribute) + " != null");
        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();
        var currentSchool = currentUserSchoolValues[currentlySelectedSchoolIdx];

        if (!currentSchool.IsAdmin)
        {
            notificationService.ErrorNotification("You are not authorised to access this page.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        // Existing email validations
        if (emailList is null)
        {
            notificationService.ErrorNotification("Invalid email list");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (emailList.Distinct().Count() != emailList.Length)
        {
            notificationService.ErrorNotification("Duplicate email addresses are not allowed.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (emailList.Length > 5)
        {
            notificationService.ErrorNotification("You can only add up to 5 email addresses.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var emailValidator = new EmailAddressAttribute();
        if (emailList.Any(email => !emailValidator.IsValid(email)))
        {
            notificationService.ErrorNotification("Invalid email address.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (string.IsNullOrWhiteSpace(cartItems))
        {
            notificationService.ErrorNotification("Invalid shopping cart items");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var shoppingCartItems = JsonConvert.DeserializeObject<List<ShoppingCartModel.ShoppingCartItemModel>>(cartItems);
        if (shoppingCartItems is { Count: <= 0 })
        {
            notificationService.ErrorNotification("No items in the shopping cart");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (string.IsNullOrWhiteSpace(quoteTitle))
        {
            notificationService.ErrorNotification("No quote title provided");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (string.IsNullOrWhiteSpace(currentSchool.LinkedTo) ||
            !int.TryParse(currentSchool.LinkedTo[4..], out var accountId))
        {
            notificationService.ErrorNotification("Unable to locate account ID for school");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        if (!int.TryParse(currentSchool.CrmContactId, out var contactId))
        {
            notificationService.ErrorNotification("Unable to locate contact ID for user");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }
        
        // Load email accounts
        var emailAccounts = await emailAccountService.GetAllEmailAccountsAsync();
        if (!emailAccounts.Any())
        {
            notificationService.ErrorNotification("No email accounts configured.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var appliedDiscounts = new List<ShoppingCartModel.DiscountBoxModel.DiscountInfoModel>();
        if (!string.IsNullOrWhiteSpace(appliedDiscountsJson))
        {
            appliedDiscounts =
                JsonConvert.DeserializeObject<List<ShoppingCartModel.DiscountBoxModel.DiscountInfoModel>>(
                    appliedDiscountsJson);
            foreach (var appliedDiscount in appliedDiscounts)
            {
                var discount = await discountService.GetDiscountByIdAsync(appliedDiscount.Id);
                shoppingCartItems.Add(
                    new ShoppingCartModel.ShoppingCartItemModel
                    {
                        Sku = $"COUPON-{appliedDiscount.CouponCode}", Quantity = 1,
                        ProductName = $"Coupon: {discount.Name}, Admin Comment: {discount.AdminComment}",
                        UnitPriceValue = Math.Abs(discount.DiscountAmount) * -1 // todo: handle percentages
                    });
            }
        }

        // Verify pricing for each shopping cart item (excluding coupons)
        foreach (var cartItem in shoppingCartItems.Where(cartItem => !cartItem.Sku.StartsWith("COUPON-")))
        {
            var pricing = await sharedDatafeedApiClient.FetchProductPricingAsync(cartItem.Sku.Trim());
            if (pricing.IsFailure)
            {
                notificationService.ErrorNotification(pricing.Error);
                Response.Headers["hx-refresh"] = "true";
                return NoContent();
            }
        }

        var (generatePdfSucceeded, _, generatePdfResponse, generatePdfError) =
            await Quoting.CreateQuoteAndGeneratePdfAsync(accountId, contactId, quoteTitle, shoppingCartItems,
                Request.Host.ToString(), productService);
        if (!generatePdfSucceeded)
        {
            // If the quote failed to generate, we want to email the dev team
            var toEmail = Debugger.IsAttached ? "<EMAIL>" : "<EMAIL>";
            await queuedEmailService.InsertQueuedEmailAsync(new QueuedEmail
            {
                From = emailAccounts.First().Email,
                FromName = "Edunet eShop",
                To = toEmail,
                Subject = "CRITICAL: Customer quote generation FAILED!",
                Body = $"Customer's ({currentUser.Email}) quote generation failed. Error: {generatePdfError}",
                EmailAccountId = emailAccounts.First().Id,
                CreatedOnUtc = DateTime.UtcNow,
                Priority = QueuedEmailPriority.High
            });
            
            notificationService.ErrorNotification(generatePdfError);
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var token = Guid.NewGuid().ToString();
        var tempPath = Path.Combine(Path.GetTempPath(), $"{token}.pdf");
        await System.IO.File.WriteAllBytesAsync(tempPath, Convert.FromBase64String(generatePdfResponse));
        Response.Headers["HX-Redirect"] = Url.Action("DownloadQuotePdf", "Quote",
            new { token, filename = $"Quote-{token}.pdf", currentlySelectedSchoolIdx });

        var currentStore = await storeContext.GetCurrentStoreAsync();
        var sendQuoteEmail = await RenderPartialViewToStringAsync(
            "~/Plugins/Widgets.Quote/EmailTemplates/SendQuote.cshtml",
            new SendQuoteModel
            {
                UserName = currentUser.FirstName + " " + currentUser.LastName,
                QuoteName = quoteTitle,
                QuoteLink = currentStore.Url.TrimEnd('/') + Url.Action("DownloadQuotePdf", "Quote",
                    new { token, filename = $"Quote-{token}.pdf", currentlySelectedSchoolIdx })
            });
        await queuedEmailService.InsertQueuedEmailAsync(new QueuedEmail
        {
            From = emailAccounts.First().Email,
            FromName = "Edunet eShop",
            To = Debugger.IsAttached ? "<EMAIL>" : currentUser.Email,
            CC = Debugger.IsAttached ? "<EMAIL>" : string.Join(", ", emailList),
            Subject = "Your quote has been generated",
            Body = sendQuoteEmail,
            EmailAccountId = emailAccounts.First().Id,
            CreatedOnUtc = DateTime.UtcNow,
            Priority = QueuedEmailPriority.High
        });

        // Clear the shopping cart
        var cart = await shoppingCartService.GetShoppingCartAsync(currentUser,
            Core.Domain.Orders.ShoppingCartType.ShoppingCart, currentStore.Id);
        foreach (var item in cart)
        {
            await shoppingCartService.DeleteShoppingCartItemAsync(item);
        }

        // Clear the discounts
        foreach (var appliedDiscount in appliedDiscounts)
        {
            var discount = await discountService.GetDiscountByIdAsync(appliedDiscount.Id);
            if (discount != null)
            {
                await customerService.RemoveDiscountCouponCodeAsync(currentUser, discount.CouponCode);
            }
        }

        return Ok();
    }

    /// <summary>
    /// Gets editable quotes for the current user's selected school.
    /// Returns only quotes that meet editability criteria.
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetEditableQuotesAsync(uint currentlySelectedSchoolIdx)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return Json(new { Success = false, Message = "Invalid user" });
        }

        // Existing school validation
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));

        if (schoolsAttribute == null)
        {
            return Json(new { Success = false, Message = "School information not found" });
        }

        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();

        if (currentlySelectedSchoolIdx >= currentUserSchoolValues.Length)
        {
            return Json(new { Success = false, Message = "Invalid school selection" });
        }

        var currentSchool = currentUserSchoolValues[(int)currentlySelectedSchoolIdx];

        if (!currentSchool.IsAdmin)
        {
            return Json(new { Success = false, Message = "You are not authorised to access quotes." });
        }

        // Get account ID
        if (string.IsNullOrWhiteSpace(currentSchool.LinkedTo) ||
            !int.TryParse(currentSchool.LinkedTo[4..], out var accountId))
        {
            return Json(new { Success = false, Message = "Unable to locate account ID for school" });
        }

        // Get editable quotes
        var result = await quoteSelectionService.GetEditableQuotesAsync(accountId);
        if (result.IsFailure)
        {
            return Json(new { Success = false, Message = result.Error });
        }

        return Json(result.Value);
    }

    /// <summary>
    /// Adds a product to the selected quote using CRM API.
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> AddProductToSelectedQuoteAsync([FromBody] AddProductToQuoteRequest request)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return Json(new { Success = false, Message = "Invalid user" });
        }

        // Existing school validation
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));

        if (schoolsAttribute == null)
        {
            return Json(new { Success = false, Message = "School information not found" });
        }

        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();

        if (request.CurrentlySelectedSchoolIdx >= currentUserSchoolValues.Length)
        {
            return Json(new { Success = false, Message = "Invalid school selection" });
        }

        var currentSchool = currentUserSchoolValues[(int)request.CurrentlySelectedSchoolIdx];

        if (!currentSchool.IsAdmin)
        {
            return Json(new { Success = false, Message = "You are not authorised to add products to quotes." });
        }

        // Get account ID
        if (string.IsNullOrWhiteSpace(currentSchool.LinkedTo) ||
            !int.TryParse(currentSchool.LinkedTo[4..], out var accountId))
        {
            return Json(new { Success = false, Message = "Unable to locate account ID for school" });
        }

        // Validate model
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { Success = false, Message = string.Join(", ", errors) });
        }

        // Add product to quote
        var result = await quoteSelectionService.AddProductToQuoteAsync(request, accountId);
        if (result.IsFailure)
        {
            return Json(new { Success = false, Message = result.Error });
        }

        return Json(result.Value);
    }

    public async Task<ActionResult> DownloadQuotePdfAsync(string token, string filename,
        uint currentlySelectedSchoolIdx)
    {
        // // Existing user validation
        // var currentUser = await _workContext.GetCurrentCustomerAsync();
        // if (currentUser == null)
        // {
        //     return BadRequest("Invalid user");
        // }
        //         
        // // Existing school validation
        // var customerAttributes = await _customerAttributeService.GetAllCustomerAttributesAsync();
        // var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
        //     string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));
        // Debug.Assert(schoolsAttribute != null, nameof(schoolsAttribute) + " != null");
        // var currentUserSchoolValues = _customerAttributeParser
        //     .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
        //     .Select(JsonConvert.DeserializeObject<School>).ToArray();
        // var currentSchool = currentUserSchoolValues[currentlySelectedSchoolIdx];
        //
        // if (currentSchool.IsAdmin == false)
        // {
        //     return BadRequest("You are not authorised to access this page.");
        // }

        var tempPath = Path.Combine(Path.GetTempPath(), $"{token}.pdf");
        if (!System.IO.File.Exists(tempPath))
        {
            notificationService.ErrorNotification("Invalid token");
            return RedirectToRoute("Login");
        }

        var bytes = await System.IO.File.ReadAllBytesAsync(tempPath);
        // System.IO.File.Delete(tempPath);
        return File(bytes, "application/pdf", filename);
    }
}