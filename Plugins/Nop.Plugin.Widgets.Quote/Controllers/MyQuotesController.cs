using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Nop.Core;
using Nop.Plugin.ExternalAuth.MultiSSO.Models;
using Nop.Plugin.Misc.EdunetCore.Common;
using Nop.Plugin.Misc.EdunetCore.Models.CrmApi;
using Nop.Plugin.Widgets.Quote.Models.MyQuotes;
using Nop.Plugin.Widgets.Quote.Services;
using Nop.Services.Customers;
using Nop.Services.Messages;
using Nop.Web.Framework.Controllers;

namespace Nop.Plugin.Widgets.Quote.Controllers;

public class MyQuotesController(
    INotificationService notificationService,
    IWorkContext workContext,
    ICustomerAttributeService customerAttributeService,
    ICustomerAttributeParser customerAttributeParser,
    IQuoteService quoteService)
    : BaseController
{
    /// <summary>
    /// Display the list of customer quotes with optional date range and search filtering
    /// </summary>
    /// <param name="accountId">The account ID for the current user</param>
    /// <param name="currentlySelectedSchoolIdx">Index of the currently selected school</param>
    /// <param name="startDate">Start date for filtering in yyyy-MM-dd format (HTML5 date input format)</param>
    /// <param name="endDate">End date for filtering in yyyy-MM-dd format (HTML5 date input format)</param>
    /// <param name="searchTerm">Optional search term to filter quotes</param>
    /// <param name="pageNumber">Current page number for pagination</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <returns>View with filtered and paginated quote list</returns>
    public async Task<IActionResult> IndexAsync(int accountId, int currentlySelectedSchoolIdx, string startDate = null, string endDate = null, string searchTerm = null, int pageNumber = 1, int pageSize = 10)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return BadRequest("Invalid user");
        }

        // Parse dates from HTML5 date input (yyyy-MM-dd format)
        // HTML5 date inputs always return dates in yyyy-MM-dd format regardless of locale
        DateTime parsedStartDate;
        DateTime parsedEndDate;
        
        // Set default date range to last 30 days if not provided
        // This ensures users see recent data on initial page load
        if (string.IsNullOrWhiteSpace(startDate) || string.IsNullOrWhiteSpace(endDate))
        {
            parsedEndDate = DateTime.Today;
            parsedStartDate = DateTime.Today.AddDays(-30);
        }
        else
        {
            // Try to parse dates with yyyy-MM-dd format (HTML5 date input format)
            // Using ParseExact with InvariantCulture ensures consistent parsing
            if (!DateTime.TryParseExact(startDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedStartDate))
            {
                ModelState.AddModelError("StartDate", "Invalid start date format");
                notificationService.ErrorNotification("Invalid start date format");
                return RedirectToAction("Index", "MyQuotes", new { accountId, currentlySelectedSchoolIdx });
            }
            
            if (!DateTime.TryParseExact(endDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedEndDate))
            {
                ModelState.AddModelError("EndDate", "Invalid end date format");
                notificationService.ErrorNotification("Invalid end date format");
                return RedirectToAction("Index", "MyQuotes", new { accountId, currentlySelectedSchoolIdx });
            }
        }

        // Validate date range - ensure logical ordering
        if (parsedStartDate > parsedEndDate)
        {
            ModelState.AddModelError("DateRange", "Start date must be before or equal to end date");
            notificationService.ErrorNotification("Start date must be before or equal to end date");
            return RedirectToAction("Index", "MyQuotes", new { accountId, currentlySelectedSchoolIdx });
        }

        // Get all quotes using the service (we'll filter in memory)
        var serviceModel = string.IsNullOrWhiteSpace(searchTerm)
            ? await quoteService.GetCustomerQuotesAsync(currentUser, accountId, currentlySelectedSchoolIdx, 1, int.MaxValue)
            : await quoteService.SearchQuotesAsync(currentUser, accountId, currentlySelectedSchoolIdx, searchTerm, 1, int.MaxValue);
        
        // Check for errors
        if (serviceModel.HasError)
        {
            notificationService.ErrorNotification(serviceModel.ErrorMessage);
            return RedirectToRoute("Login");
        }

        // Apply date filtering on DateCreated field using LINQ
        // We perform in-memory filtering after retrieving all quotes from the API
        // This ensures compatibility with the external service while providing flexible filtering
        // DateCreated is stored as a string, so we parse it to DateTime for comparison
        var filteredQuotes = serviceModel.Quotes
            .Where(q => {
                // Parse the DateCreated string to DateTime for comparison
                if (DateTime.TryParse(q.DateCreated, out var quoteDate))
                {
                    return quoteDate.Date >= parsedStartDate.Date && quoteDate.Date <= parsedEndDate.Date;
                }
                return false; // Exclude quotes with invalid dates
            })
            .ToList();

        // Apply pagination logic to filtered results
        // Calculate total pages based on filtered results count
        var totalPages = (int)Math.Ceiling(filteredQuotes.Count / (double)pageSize);
        var pagedQuotes = filteredQuotes
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        // Create IndexViewModel and copy base properties
        var model = new IndexViewModel
        {
            // Copy all properties from serviceModel to model
            Customer = serviceModel.Customer,
            CurrentSchool = serviceModel.CurrentSchool,
            CurrentPage = pageNumber,
            PageSize = pageSize,
            TotalPages = totalPages,
            TotalQuotes = filteredQuotes.Count,
            SearchTerm = serviceModel.SearchTerm,
            CurrentlySelectedSchoolIdx = serviceModel.CurrentlySelectedSchoolIdx,
            IsLoading = serviceModel.IsLoading,
            ErrorMessage = serviceModel.ErrorMessage,
            StartDate = parsedStartDate,
            EndDate = parsedEndDate
        };

        // Copy filtered and paged quotes to the base class property
        ((QuotesIndexModel)model).Quotes = pagedQuotes;

        return View("~/Plugins/Widgets.Quote/Views/MyQuotes/Index.cshtml", model);
    }
    

    [HttpPost]
    public async Task<IActionResult> ViewQuotePdfAsync([FromForm] int quoteId, [FromForm] uint currentlySelectedSchoolIdx)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return BadRequest("Invalid user");
        }

        // Existing school validation
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));
        Debug.Assert(schoolsAttribute != null, nameof(schoolsAttribute) + " != null");
        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();
        var currentSchool = currentUserSchoolValues[currentlySelectedSchoolIdx];

        if (!currentSchool.IsAdmin)
        {
            notificationService.ErrorNotification("You are not authorised to access this page.");
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var crmBaseUrl = Request.Host.ToString().Trim()
            .Contains("nop.edunet.com.au".Trim(), StringComparison.CurrentCultureIgnoreCase)
            ? "https://crm-beta.edunet.com.au"
            : CrmApiHttpClient.CrmBaseUrl;
        var crmApiHttpClient = new CrmApiHttpClient(crmBaseUrl);
        var (pdfGenerationSucceeded, _, generateQuotePdfResponse, pdfGenerationError) =
            await crmApiHttpClient.GenerateQuotePdfAsync(quoteId);
        if (!pdfGenerationSucceeded)
        {
            notificationService.ErrorNotification(pdfGenerationError);
            Response.Headers["hx-refresh"] = "true";
            return NoContent();
        }

        var token = Guid.NewGuid().ToString();
        var tempPath = Path.Combine(Path.GetTempPath(), $"{token}.pdf");
        await System.IO.File.WriteAllBytesAsync(tempPath,
            Convert.FromBase64String(generateQuotePdfResponse.QuoteContent));

        Response.Headers["HX-Redirect"] = Url.Action("DownloadQuotePdf", "Quote",
            new { token, filename = $"Quote-{quoteId}.pdf", currentlySelectedSchoolIdx });
        return NoContent();
    }

    public async Task<IActionResult> ComingSoonAsync(int currentlySelectedSchoolIdx)
    {
        // Existing user validation
        var currentUser = await workContext.GetCurrentCustomerAsync();
        if (currentUser == null)
        {
            return BadRequest("Invalid user");
        }

        // Existing school validation
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));
        Debug.Assert(schoolsAttribute != null, nameof(schoolsAttribute) + " != null");
        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(currentUser.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();
        var currentSchool = currentUserSchoolValues[currentlySelectedSchoolIdx];

        if (!currentSchool.IsAdmin)
        {
            notificationService.ErrorNotification("You are not authorised to access this page.");
            return RedirectToRoute("Login");
        }

        return View("~/Plugins/Widgets.Quote/Views/MyQuotes/ComingSoon.cshtml");
    }
}