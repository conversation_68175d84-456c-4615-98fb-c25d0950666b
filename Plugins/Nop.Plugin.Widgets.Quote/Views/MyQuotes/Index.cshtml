@using Nop.Web.Components
@model Nop.Plugin.Widgets.Quote.Models.MyQuotes.IndexViewModel
@{
    Layout = "_ColumnsTwo";
    ViewBag.PageTitle = "My Quotes";
}

@section left
{
    @await Component.InvokeAsync(typeof(CustomerNavigationViewComponent), new { selectedTabId = Nop.Plugin.Widgets.Quote.Components.MyQuotesWidget.MY_QUOTES_TAB_INDEX })
}

<link rel="stylesheet" href="~/Plugins/Widgets.Quote/wwwroot/css/my-quotes.css" asp-append-version="true"/>

<div class="page my-quotes-page edunet-page-container animate-fadeInUp">
    <!-- Header Section -->
    <div class="quote-header edunet-page-header">
        <div class="edunet-page-header-content">
            <div class="edunet-page-title-section">
                <h1 class="edunet-page-title">My Quotes</h1>
                @if (Model.CurrentSchool != null && !string.IsNullOrEmpty(Model.CurrentSchool.Name))
                {
                    <p class="edunet-school-name">
                        <span class="edunet-school-name-label">School:</span> @Model.CurrentSchool.Name
                    </p>
                }
            </div>

            <!-- Quote Count Badge -->
            <div class="edunet-count-badge-container">
                <div class="edunet-count-badge">
                    <span class="edunet-count-badge-number">@Model.TotalQuotes</span>
                    @(Model.TotalQuotes == 1 ? "Quote" : "Quotes")
                </div>
            </div>
        </div>
    </div>

    <!-- Date Filter Section -->
    <div class="filter-section">
        <form method="get" action="@Url.Action("Index", "MyQuotes")" class="edunet-filter-form">
            <input type="hidden" name="accountId" value="@Context.Request.Query["accountId"]"/>
            <input type="hidden" name="currentlySelectedSchoolIdx" value="@Model.CurrentlySelectedSchoolIdx"/>
            <div class="edunet-filter-container">
                <div class="edunet-filter-inputs">
                    <div class="edunet-filter-field">
                        <label for="startDate">Start Date</label>
                        <input type="date" 
                               id="startDate" 
                               name="startDate" 
                               value="@Model.StartDate.ToString("yyyy-MM-dd")"
                               class="edunet-date-input">
                    </div>
                    <div class="edunet-filter-field">
                        <label for="endDate">End Date</label>
                        <input type="date" 
                               id="endDate" 
                               name="endDate" 
                               value="@Model.EndDate.ToString("yyyy-MM-dd")"
                               class="edunet-date-input">
                    </div>
                    <div class="edunet-filter-actions">
                        <button type="submit" class="edunet-btn edunet-btn-primary">Apply Filters</button>
                        <a href="@Url.Action("Index", "MyQuotes", new { accountId = Context.Request.Query["accountId"], currentlySelectedSchoolIdx = Model.CurrentlySelectedSchoolIdx })" 
                           class="edunet-btn edunet-btn-secondary">Clear Filters</a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Search Section -->
    <div class="edunet-search-section">
        <div class="edunet-search-container">
            <form method="get" action="@Url.Action("Index", "MyQuotes")" class="edunet-search-form"
                  id="quote-search-form">
                <input type="hidden" name="accountId" value="@Context.Request.Query["accountId"]"/>
                <input type="hidden" name="currentlySelectedSchoolIdx" value="@Model.CurrentlySelectedSchoolIdx"/>
                <input type="hidden" name="startDate" value="@Model.StartDate.ToString("yyyy-MM-dd")"/>
                <input type="hidden" name="endDate" value="@Model.EndDate.ToString("yyyy-MM-dd")"/>
                <div class="edunet-search-input-wrapper">
                    <label for="searchTerm" class="sr-only">Search quotes</label>
                    <input type="text"
                           id="searchTerm"
                           name="searchTerm"
                           value="@Model.SearchTerm"
                           placeholder="Search by quote title, ID, or status..."
                           class="edunet-search-input">
                </div>
                <div class="edunet-search-buttons">
                    <button type="submit"
                            class="edunet-btn edunet-btn-primary">
                        <span class="max-sm:hidden">Search</span>
                        <svg class="edunet-search-icon sm:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                             width="20" height="20">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    @if (!string.IsNullOrEmpty(Model.SearchTerm))
                    {
                        <a href="@Url.Action("Index", "MyQuotes", new { accountId = Context.Request.Query["accountId"], currentlySelectedSchoolIdx = Model.CurrentlySelectedSchoolIdx, startDate = Model.StartDate.ToString("yyyy-MM-dd"), endDate = Model.EndDate.ToString("yyyy-MM-dd") })"
                           class="edunet-btn edunet-btn-secondary">
                            Clear
                        </a>
                    }
                </div>
            </form>
        </div>
    </div>

    <!-- Main Content Area -->
    <div id="quotes-content">
        @await Html.PartialAsync("~/Plugins/Widgets.Quote/Views/MyQuotes/_QuotesList.cshtml", Model)
    </div>
</div>

<script src="https://unpkg.com/htmx.org@1.9.2"></script>
<script src="~/Plugins/Widgets.Quote/wwwroot/js/my-quotes.js" asp-append-version="true"></script>
<script>
    // Initialise HTMX for PDF viewing
    document.addEventListener('DOMContentLoaded', function () {
        // PDF download handling
        document.addEventListener('htmx:afterRequest', function (event) {
            if (event.detail.elt.hasAttribute('data-pdf-button')) {
                // Hide loader after request
                const loader = document.getElementById('quote-loader');
                if (loader) {
                    loader.style.display = 'none';
                }
            }
        });

        document.addEventListener('htmx:responseError', function (event) {
            const loader = document.getElementById('quote-loader');
            if (loader) {
                loader.style.display = 'none';
            }
        });
    });
</script>