using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Nop.Plugin.Widgets.Quote.Models.QuoteEdit;

namespace Nop.Plugin.Widgets.Quote.Services;

/// <summary>
/// Service interface for quote editing operations.
/// All methods return customer-safe data with sensitive cost information filtered out.
/// </summary>
public interface IQuoteEditService
{
    /// <summary>
    /// Retrieves quote details for editing interface.
    /// Returns customer-safe data with all buy prices and profit data filtered out.
    /// </summary>
    /// <param name="quoteId">The ID of the quote to retrieve</param>
    /// <param name="accountId">The account ID for access validation</param>
    /// <returns>Result containing customer-safe quote edit view model or error message</returns>
    Task<Result<EditQuoteViewModel>> GetQuoteForEditingAsync(int quoteId, int accountId);

    /// <summary>
    /// Updates the quantity of a specific line item.
    /// Returns customer-safe response data with updated totals.
    /// </summary>
    /// <param name="request">The quantity update request</param>
    /// <param name="accountId">The account ID for access validation</param>
    /// <returns>Result containing customer-safe AJAX response or error message</returns>
    Task<Result<QuoteEditAjaxResponse>> UpdateLineItemQuantityAsync(UpdateLineItemQuantityRequest request, int accountId);

    /// <summary>
    /// Removes a line item from the quote.
    /// Returns customer-safe response data with updated totals.
    /// </summary>
    /// <param name="request">The line item removal request</param>
    /// <param name="accountId">The account ID for access validation</param>
    /// <returns>Result containing customer-safe AJAX response or error message</returns>
    Task<Result<QuoteEditAjaxResponse>> RemoveLineItemAsync(RemoveLineItemRequest request, int accountId);

    /// <summary>
    /// Checks if a quote can be edited based on business rules.
    /// Lightweight method for quick editability validation.
    /// </summary>
    /// <param name="quoteId">The ID of the quote to check</param>
    /// <param name="accountId">The account ID for access validation</param>
    /// <returns>Result containing editability status or error message</returns>
    Task<Result<bool>> IsQuoteEditableAsync(int quoteId, int accountId);
}
