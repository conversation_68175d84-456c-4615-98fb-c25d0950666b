using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Newtonsoft.Json;
using Nop.Core;
using Nop.Core.Caching;
using Nop.Core.Domain.Customers;
using Nop.Plugin.ExternalAuth.MultiSSO.Models;
using Nop.Plugin.Misc.EdunetCore.Common;
using Nop.Plugin.Misc.EdunetCore.Models.CrmApi;
using Nop.Plugin.Widgets.Quote.Models.MyQuotes;
using Nop.Services.Customers;

namespace Nop.Plugin.Widgets.Quote.Services;

public class QuoteService(
    ICustomerAttributeService customerAttributeService,
    ICustomerAttributeParser customerAttributeParser,
    IStaticCacheManager cacheManager,
    IWebHelper webHelper)
    : IQuoteService
{
    private const string QUOTE_CREATED = "Quote Created";

    public async Task<QuotesIndexModel> GetCustomerQuotesAsync(
        Customer customer,
        int accountId,
        int schoolIndex,
        int pageNumber = 1,
        int pageSize = 10)
    {
        var model = new QuotesIndexModel
        {
            Customer = customer,
            CurrentlySelectedSchoolIdx = schoolIndex,
            CurrentPage = pageNumber,
            PageSize = pageSize
        };

        try
        {
            // Get current school
            var schoolResult = await GetCurrentSchoolAsync(customer, schoolIndex);
            if (schoolResult.IsFailure)
            {
                model.ErrorMessage = schoolResult.Error;
                return model;
            }

            model.CurrentSchool = schoolResult.Value;

            // Get quotes from CRM
            var quotesResult = await GetQuotesFromCrmAsync(accountId);
            if (quotesResult.IsFailure)
            {
                model.ErrorMessage = quotesResult.Error;
                return model;
            }

            // Filter quotes based on user permissions
            var filteredQuotes = FilterQuotesForUser(quotesResult.Value, model.CurrentSchool);

            // Apply pagination
            model.TotalQuotes = filteredQuotes.Count;
            model.TotalPages = (int)Math.Ceiling(model.TotalQuotes / (double)pageSize);

            // Ensure current page is valid
            if (pageNumber > model.TotalPages && model.TotalPages > 0)
            {
                model.CurrentPage = model.TotalPages;
            }

            // Get quotes for current page
            var pagedQuotes = filteredQuotes
                .Skip((model.CurrentPage - 1) * pageSize)
                .Take(pageSize)
                .Select(q => new QuoteItemModel(q))
                .ToList();

            model.Quotes = pagedQuotes;
        }
        catch (Exception)
        {
            model.ErrorMessage = "An error occurred while loading quotes. Please try again.";
        }

        return model;
    }

    public async Task<QuotesIndexModel> SearchQuotesAsync(
        Customer customer,
        int accountId,
        int schoolIndex,
        string searchTerm,
        int pageNumber = 1,
        int pageSize = 10)
    {
        var model = await GetCustomerQuotesAsync(customer, accountId, schoolIndex, 1, int.MaxValue);
        
        if (!model.HasError && !string.IsNullOrWhiteSpace(searchTerm))
        {
            // Filter quotes by search term
            var searchLower = searchTerm.ToLower();
            model.Quotes = model.Quotes.Where(q =>
                q.QuotationTitle?.ToLower().Contains(searchLower, StringComparison.CurrentCultureIgnoreCase) == true ||
                q.Id.ToString().Contains(searchLower) ||
                q.QuoteStatus?.ToLower().Contains(searchLower, StringComparison.CurrentCultureIgnoreCase) == true
            ).ToList();

            // Re-apply pagination
            model.TotalQuotes = model.Quotes.Count;
            model.TotalPages = (int)Math.Ceiling(model.TotalQuotes / (double)pageSize);
            model.CurrentPage = pageNumber;
            model.PageSize = pageSize;

            // Ensure current page is valid
            if (pageNumber > model.TotalPages && model.TotalPages > 0)
            {
                model.CurrentPage = model.TotalPages;
            }

            model.Quotes = model.Quotes
                .Skip((model.CurrentPage - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        model.SearchTerm = searchTerm;
        return model;
    }

    public async Task<int> GetQuoteCountAsync(Customer customer, int accountId)
    {
        try
        {
            var quotesResult = await GetQuotesFromCrmAsync(accountId);
            if (quotesResult.IsFailure)
            {
                return 0;
            }

            // For now, return total count without filtering
            // This could be enhanced to respect user permissions
            return quotesResult.Value.Count;
        }
        catch
        {
            return 0;
        }
    }

    private async Task<Result<School>> GetCurrentSchoolAsync(Customer customer, int schoolIndex)
    {
        var customerAttributes = await customerAttributeService.GetAllCustomerAttributesAsync();
        var schoolsAttribute = customerAttributes.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "Schools".Trim(), StringComparison.CurrentCultureIgnoreCase));
        
        if (schoolsAttribute == null)
        {
            return Result.Failure<School>("Schools attribute not found");
        }

        var currentUserSchoolValues = customerAttributeParser
            .ParseValues(customer.CustomCustomerAttributesXML, schoolsAttribute.Id)
            .Select(JsonConvert.DeserializeObject<School>).ToArray();

        if (schoolIndex >= currentUserSchoolValues.Length)
        {
            return Result.Failure<School>("Invalid school index");
        }

        return Result.Success(currentUserSchoolValues[schoolIndex]);
    }

    private async Task<Result<List<GetQuotesForSchoolResponse.QuoteItem>>> GetQuotesFromCrmAsync(int accountId)
    {
        // Create cache key with 5 minute cache time
        var cacheKey = new CacheKey($"nop.plugin.widgets.quote.quotes.{accountId}")
        {
            CacheTime = 5 // 5 minutes
        };
        
        var cachedQuotes = await cacheManager.GetAsync(cacheKey, async () =>
        {
            var crmBaseUrl = webHelper.GetStoreLocation().Trim()
                .Contains("nop.edunet.com.au".Trim(), StringComparison.CurrentCultureIgnoreCase)
                ? "https://crm-beta.edunet.com.au"
                : CrmApiHttpClient.CrmBaseUrl;
            
            var crmApi = new CrmApiHttpClient(crmBaseUrl);
            var result = await crmApi.GetQuotesForSchoolAsync(accountId);
            
            return result.IsSuccess ? result.Value.Quotes : null;
        });

        if (cachedQuotes == null)
        {
            return Result.Failure<List<GetQuotesForSchoolResponse.QuoteItem>>("Failed to retrieve quotes from CRM");
        }

        return Result.Success(cachedQuotes);
    }

    private List<GetQuotesForSchoolResponse.QuoteItem> FilterQuotesForUser(
        List<GetQuotesForSchoolResponse.QuoteItem> quotes,
        School currentSchool)
    {
        IEnumerable<GetQuotesForSchoolResponse.QuoteItem> relevantQuotes;

        if (currentSchool.IsAdmin)
        {
            // Admin: Apply the filter logic to all school quotes
            relevantQuotes = quotes.Where(FilterLogic);
        }
        else
        {
            // Non-admin: Apply filter logic, but only to their own quotes (ContactID match)
            relevantQuotes = int.TryParse(currentSchool.CrmContactId, out var userCrmContactId)
                ? quotes.Where(q => q.ContactID == userCrmContactId && FilterLogic(q))
                : [];
        }

        return relevantQuotes.ToList();
    }

    private bool FilterLogic(GetQuotesForSchoolResponse.QuoteItem q)
    {
        if (!IsRecent(q)) // Common filter: must be recent
        {
            return false;
        }

        // Check for "E-Comm" type
        var isEcomm = string.Equals(q.QuoteType?.Trim(), "E-Comm", StringComparison.CurrentCultureIgnoreCase);

        return isEcomm ||
               // If E-Comm, show regardless of 'finalised' status (as long as recent)
               // Otherwise, quote must be finalised
               IsFinalised(q);
    }

    private static bool IsRecent(GetQuotesForSchoolResponse.QuoteItem q)
    {
        if (DateTime.TryParseExact(q.DateCreated, "dd-MM-yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None,
                out var dateCreated))
        {
            return dateCreated >= DateTime.Now.AddMonths(-18);
        }

        return false;
    }

    private static bool IsFinalised(GetQuotesForSchoolResponse.QuoteItem q) => !q.QuoteStatus.Trim()
        .Equals(QUOTE_CREATED.Trim(), StringComparison.CurrentCultureIgnoreCase);
}