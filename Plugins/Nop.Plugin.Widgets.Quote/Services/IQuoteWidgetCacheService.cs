using System.Threading.Tasks;
using Nop.Core.Domain.Customers;
using Nop.Plugin.ExternalAuth.MultiSSO.Models;

namespace Nop.Plugin.Widgets.Quote.Services
{
    /// <summary>
    /// Service for caching quote widget data within the scope of a single HTTP request
    /// to avoid repeated database queries when rendering multiple product widgets
    /// </summary>
    public interface IQuoteWidgetCacheService
    {
        /// <summary>
        /// Gets cached customer data for the current request
        /// </summary>
        Task<Customer> GetCurrentCustomerAsync();

        /// <summary>
        /// Gets cached school values for the current customer
        /// </summary>
        Task<School[]> GetCurrentUserSchoolValuesAsync();

        /// <summary>
        /// Gets the currently selected school index from cookie (cached per request)
        /// </summary>
        int? GetCurrentlySelectedSchoolIndex();

        /// <summary>
        /// Sets the currently selected school index for caching
        /// </summary>
        void SetCurrentlySelectedSchoolIndex(int? index);

        /// <summary>
        /// Gets whether the current user is a school admin for the selected school
        /// </summary>
        Task<bool> IsCurrentUserSchoolAdminAsync();

        /// <summary>
        /// Gets the account ID for the currently selected school
        /// </summary>
        Task<int?> GetCurrentSchoolAccountIdAsync();
    }
}