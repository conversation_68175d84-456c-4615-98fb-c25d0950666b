namespace Nop.Plugin.Widgets.Quote.Models.QuoteSelection
{
    /// <summary>
    /// Response model for adding a product to a quote.
    /// Contains only customer-safe information without cost data.
    /// </summary>
    public class AddProductToQuoteResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string QuoteTitle { get; set; }
        public int ProductsAdded { get; set; }
        
        /// <summary>
        /// Customer-safe totals (sell prices only)
        /// </summary>
        public CustomerSafeTotals Totals { get; set; }
    }
    
    /// <summary>
    /// Customer-safe quote totals excluding all cost and profit data
    /// </summary>
    public class CustomerSafeTotals
    {
        public decimal SubtotalExGst { get; set; }
        public decimal GstAmount { get; set; }
        public decimal TotalIncGst { get; set; }
        public string FormattedSubtotalExGst { get; set; }
        public string FormattedGstAmount { get; set; }
        public string FormattedTotalIncGst { get; set; }
    }
}
