<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Copyright>Copyright © SolutionOne</Copyright>
        <Company>SolutionOne</Company>
        <Authors>SolutionOne</Authors>
        <PackageLicenseUrl></PackageLicenseUrl>
        <PackageProjectUrl>https://www.nopcommerce.com/</PackageProjectUrl>
        <RepositoryUrl>https://github.com/nopSolutions/nopCommerce</RepositoryUrl>
        <RepositoryType>Git</RepositoryType>
        <OutputPath>..\..\Presentation\Nop.Web\Plugins\Misc.EdunetCore</OutputPath>
        <OutDir>$(OutputPath)</OutDir>
        <!--Set this parameter to true to get the dlls copied from the NuGet cache to the output of your project.
        You need to set this parameter to true if your plugin has a nuget package 
        to ensure that the dlls copied from the NuGet cache to the output of your project-->
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <RootNamespace>Nop.Plugin.Misc.EdunetCore</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <None Update="plugin.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Libraries\Nop.Core\Nop.Core.csproj"/>
        <ProjectReference Include="..\..\Libraries\Nop.Services\Nop.Services.csproj"/>
        <ProjectReference Include="..\..\Presentation\Nop.Web\Nop.Web.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Remove="Views\EdunetCore.cshtml"/>
        <Content Include="Views\EdunetCore.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="CacheCow.Client" Version="2.13.1"/>
        <PackageReference Include="CSharpFunctionalExtensions" Version="3.6.0"/>
        <PackageReference Include="LogErrorsToAutotask.NETCore" Version="1.6.0" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Common\EdunetLogo.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>EdunetLogo.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Common\EdunetLogo.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>EdunetLogo.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <!-- This target execute after "Build" target -->
    <Target Name="NopTarget" AfterTargets="Build">
        <!-- Delete unnecessary libraries from plugins path -->
        <MSBuild Projects="@(ClearPluginAssemblies)" Properties="PluginPath=$(MSBuildProjectDirectory)\$(OutDir)" Targets="NopClear"/>
    </Target>
</Project>
