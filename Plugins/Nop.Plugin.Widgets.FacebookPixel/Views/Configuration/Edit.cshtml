@model FacebookPixelModel

@{
    Layout = "_AdminLayout";
    ViewBag.PageTitle = T("Admin.Common.Edit").Text;
}

<form asp-controller="FacebookPixel" asp-action="Edit" method="post">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Common.Edit")
            <small>
                <i class="fas fa-arrow-circle-left"></i>
                <a asp-action="Configure" asp-controller="FacebookPixel">@T("Common.Back")</a>
            </small>
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            <span id="delete" class="btn bg-red">
                <i class="far fa-trash-alt"></i>
                @T("Admin.Common.Delete")
            </span>
        </div>
    </div>
    @await Html.PartialAsync("~/Plugins/Widgets.FacebookPixel/Views/Configuration/_CreateOrUpdate.cshtml", Model)
</form>
<nop-delete-confirmation asp-model-id="@Model.Id" asp-button-id="delete" />