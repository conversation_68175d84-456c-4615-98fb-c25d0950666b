@model Nop.Plugin.Pickup.PickupInStore.Models.StorePickupPointModel
@{
    Layout = "~/Areas/Admin/Views/Shared/_AdminPopupLayout.cshtml";
}

<form asp-controller="PickupInStore" asp-action="Create"
      asp-route-btnId="@Context.Request.Query["btnId"]"
      asp-route-formId="@Context.Request.Query["formId"]">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Common.AddNew")
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
        </div>
    </div>

    <div class="content">
        <div class="form-horizontal">
            @await Html.PartialAsync("~/Plugins/Pickup.PickupInStore/Views/_CreateOrUpdate.cshtml", Model)
        </div>
    </div>
</form>