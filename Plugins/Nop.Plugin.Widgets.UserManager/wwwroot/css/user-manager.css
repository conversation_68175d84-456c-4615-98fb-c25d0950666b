/* User Manager */

/* CSS Variables for theming */
:root {
    --um-primary: #4ab2f1;
    --um-danger: #e74c3c;
    --um-secondary: #6b7280;
    --um-bg-primary: #ffffff;
    --um-bg-secondary: #f9fafb;
    --um-bg-info: #eff6ff;
    --um-bg-hover: #f3f4f6;
    --um-border: #d1d5db;
    --um-border-info: #bfdbfe;
    --um-text-primary: #111827;
    --um-text-secondary: #6b7280;
    --um-font-sans: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Button styles removed - now using shared edunet-btn styles from EdunetCore */


/* User Manager Page Styles */
.user-manager-page {
    font-family: var(--um-font-sans);
}

/* Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Apply animations */
.animate-fadeInUp {
    animation: fadeInUp 0.4s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.3s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.35s ease-out;
}

/* Card styling */
.um-card {
    background-color: var(--um-bg-primary);
    border: 1px solid var(--um-border);
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.um-info-card {
    background-color: var(--um-bg-info);
    border-color: var(--um-border-info);
}

/* Text styling */
.um-text-primary {
    color: var(--um-text-primary);
}

.um-text-secondary {
    color: var(--um-text-secondary);
}

.um-primary-link {
    color: var(--um-primary);
}

.um-info-icon {
    color: var(--um-primary);
}

/* Input styling */
.um-input {
    border-color: var(--um-border);
    background-color: var(--um-bg-primary);
    color: var(--um-text-primary);
}

.um-input:focus {
    ring-color: var(--um-primary);
    border-color: var(--um-primary);
}

/* Grid layout for user cards */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

/* Stagger animations for cards */
.user-card:nth-child(1) { animation-delay: 0.08s; }
.user-card:nth-child(2) { animation-delay: 0.16s; }
.user-card:nth-child(3) { animation-delay: 0.24s; }
.user-card:nth-child(4) { animation-delay: 0.32s; }
.user-card:nth-child(5) { animation-delay: 0.40s; }
.user-card:nth-child(6) { animation-delay: 0.48s; }
.user-card:nth-child(7) { animation-delay: 0.56s; }
.user-card:nth-child(8) { animation-delay: 0.64s; }
.user-card:nth-child(9) { animation-delay: 0.72s; }
.user-card:nth-child(10) { animation-delay: 0.80s; }

/* User card hover effect */
.user-card {
    transition: all 0.2s ease-in-out;
}

.user-card:hover {
    transform: translateY(-2px);
}


/* Status badge animations */
@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* Loading spinner animation removed - now using shared edunet-loading-spinner from EdunetCore */

/* Transform utility classes for buttons */
.transform {
    transform: translateZ(0);
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

.active\:scale-95:active {
    transform: scale(0.95);
}


/* Modal styling to match page theme */
.modal {
    font-family: var(--um-font-sans);
}

.modal-content {
    border: 1px solid var(--um-border);
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid var(--um-border);
}

.modal-footer {
    border-top: 1px solid var(--um-border);
}

/* Responsive styles */
@media (max-width: 640px) {
    
    /* Mobile search section improvements */
    .user-search-section .flex.flex-col.gap-4 {
        gap: 1rem;
    }
    
    .user-search-section form .flex.gap-2 {
        width: 100%;
        justify-content: flex-start;
    }
    
    .user-search-section .flex.justify-end {
        justify-content: stretch;
    }
    
    .user-search-section .flex.justify-end button {
        width: 100%;
        justify-content: center;
    }
    
    /* Legacy filter-search-section (if still exists) */
    .filter-search-section form {
        flex-direction: column;
    }
    
    .filter-search-section .flex-1 {
        width: 100%;
        max-width: none;
    }
    
    /* Responsive grid for mobile */
    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Override some nopCommerce defaults for consistency */
.user-manager-page input[type="text"],
.user-manager-page input[type="email"],
.user-manager-page input[type="password"],
.user-manager-page textarea,
.user-manager-page select {
    font-family: var(--um-font-sans);
}

/* Ensure proper focus states */
.user-manager-page input:focus,
.user-manager-page select:focus,
.user-manager-page textarea:focus {
    outline: none;
    border-color: var(--um-primary);
    box-shadow: 0 0 0 3px rgba(74, 178, 241, 0.1);
}

/* Responsive grid */
.user-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .user-grid {
        grid-template-columns: 1fr !important;
    }
}

/* Modal animations */
.modal-backdrop {
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Input focus override with CSS variables */
input:focus {
    border-color: var(--um-primary) !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(74, 178, 241, 0.1) !important;
}