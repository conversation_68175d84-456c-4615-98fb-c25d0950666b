<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Copyright>Copyright © SolutionOne</Copyright>
        <Company>SolutionOne</Company>
        <Authors>SolutionOne</Authors>
        <PackageLicenseUrl></PackageLicenseUrl>
        <PackageProjectUrl>https://www.nopcommerce.com/</PackageProjectUrl>
        <RepositoryUrl>https://github.com/nopSolutions/nopCommerce</RepositoryUrl>
        <RepositoryType>Git</RepositoryType>
        <OutputPath>..\..\Presentation\Nop.Web\Plugins\Widgets.UserManager</OutputPath>
        <OutDir>$(OutputPath)</OutDir>
        <!--Set this parameter to true to get the dlls copied from the NuGet cache to the output of your project.
        You need to set this parameter to true if your plugin has a nuget package 
        to ensure that the dlls copied from the NuGet cache to the output of your project-->
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <RootNamespace>Nop.Plugin.Widgets.UserManager</RootNamespace>
    </PropertyGroup>
    <ItemGroup>
        <None Update="plugin.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="wwwroot\css\user-manager.css">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Libraries\Nop.Services\Nop.Services.csproj"/>
        <ProjectReference Include="..\..\Presentation\Nop.Web.Framework\Nop.Web.Framework.csproj"/>
        <ProjectReference Include="..\..\Presentation\Nop.Web\Nop.Web.csproj"/>
        <ProjectReference Include="..\Nop.Plugin.ExternalAuth.MultiSSO\Nop.Plugin.ExternalAuth.MultiSSO.csproj"/>
        <ProjectReference Include="..\Nop.Plugin.Misc.EdunetCore\Nop.Plugin.Misc.EdunetCore.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <None Remove="Views\UserManagerWidget.cshtml"/>
        <Content Include="Views\UserManagerWidget.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <None Remove="Views\UserManager\Index.cshtml"/>
        <Content Include="Views\UserManager\Index.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <Content Update="EmailTemplates\InviteEmail.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <None Remove="EmailTemplates\InviteEmail.cshtml"/>
        <Content Include="EmailTemplates\InviteEmail.cshtml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Compile Update="EmailTemplates\EdunetLogo.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>EdunetLogo.resx</DependentUpon>
        </Compile>
        <Compile Update="EmailTemplates\EdunetLogo.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>EdunetLogo.resx</DependentUpon>
        </Compile>
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Update="EmailTemplates\EdunetLogo.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>EdunetLogo.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <!-- This target execute after "Build" target -->
    <Target Name="NopTarget" AfterTargets="Build">
        <!-- Delete unnecessary libraries from plugins path -->
        <MSBuild Projects="@(ClearPluginAssemblies)" Properties="PluginPath=$(MSBuildProjectDirectory)\$(OutDir)" Targets="NopClear"/>
    </Target>
</Project>
