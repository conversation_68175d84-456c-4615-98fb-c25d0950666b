using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nop.Plugin.Widgets.EnhanceProductQuantitySelector.Models;
using Nop.Web.Framework.Components;
using Nop.Web.Framework.Infrastructure;

namespace Nop.Plugin.Widgets.EnhanceProductQuantitySelector.Components;

public class EnhanceProductQuantitySelectorWidget : NopViewComponent
{
    public Task<IViewComponentResult> InvokeAsync(string widgetZone, object additionalData)
    {
        var widgetModel = new EnhanceProductQuantitySelectorWidgetModel
        {
            WidgetZone = widgetZone
        };
        
        return Task.FromResult<IViewComponentResult>(View(
            "~/Plugins/Widgets.EnhanceProductQuantitySelector/Views/EnhanceProductQuantitySelectorWidget.cshtml", widgetModel));
    }
}