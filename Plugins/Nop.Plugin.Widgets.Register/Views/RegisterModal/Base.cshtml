@using Nop.Plugin.Widgets.Register

<div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" id="registerModal" style="display: none;">
    <div class="relative p-4 w-full max-w-5xl mx-auto">
        <div class="relative bg-white rounded-lg shadow overflow-y-auto max-h-[85vh] md:max-h-[95vh]">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-2xl font-semibold tracking-tight text-gray-800" id="registerModalLabel">School Registration</h3>
                <button type="button" class="bg-transparent text-gray-400 hover:text-gray-900" onclick="closeRegisterModal()">
                    <span class="sr-only">Close</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <!-- Breadcrumb Navigation -->
            <div id="registerModalBreadcrumb" class="px-8 pt-6 pb-2">
                <div class="flex justify-between items-center w-[80%] mx-auto">
                    <!-- Step 1: Authenticate -->
                    <div class="flex flex-col items-center relative">
                        <div class="step-circle flex items-center justify-center w-10 h-10 rounded-full border-2 text-center font-medium" data-step="1">
                            <span class="step-number">1</span>
                            <svg class="step-check hidden w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="step-label mt-2 text-sm font-medium">Authenticate</p>
                    </div>
                    
                    <!-- Connector Line 1-2 -->
                    <div class="step-connector flex-1 h-0.5 mx-2"></div>
                    
                    <!-- Step 2: Enter School Details -->
                    <div class="flex flex-col items-center relative">
                        <div class="step-circle flex items-center justify-center w-10 h-10 rounded-full border-2 text-center font-medium" data-step="2">
                            <span class="step-number">2</span>
                            <svg class="step-check hidden w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="step-label mt-2 text-sm font-medium">Enter School Details</p>
                    </div>
                    
                    <!-- Connector Line 2-3 -->
                    <div class="step-connector flex-1 h-0.5 mx-2"></div>
                    
                    <!-- Step 3: Review -->
                    <div class="flex flex-col items-center relative">
                        <div class="step-circle flex items-center justify-center w-10 h-10 rounded-full border-2 text-center font-medium" data-step="3">
                            <span class="step-number">3</span>
                            <svg class="step-check hidden w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="step-label mt-2 text-sm font-medium">Review</p>
                    </div>
                </div>
            </div>
            <div id="registerModalContent" class="px-8 py-10 flex flex-col-reverse md:flex-row md:space-x-6">
                <div class="w-full md:w-2/3">
                    <p class="text-base text-gray-700 leading-relaxed mb-8 text-left tracking-normal">
                        The Edunet eShop is exclusively for <strong>schools, TAFEs, and educational
                            institutions.</strong><br/>
                        We do not supply products for <strong>personal use or individual consumer purchases.</strong>
                    </p>
                    <p class="text-base text-blue-500 leading-relaxed mb-8 text-left tracking-normal">
                        If you are a parent looking to purchase through a <strong>BYOD (Bring Your Own Device)
                            program</strong>, please use the unique purchasing link provided by your school.<br/>
                        If you need any assistance, feel free to contact our team between <strong>9:00 AM and 5:00 PM,
                            Monday to Friday</strong>, or email us at <a href="mailto:<EMAIL>"
                                                                         class="text-blue-700 hover:underline"><EMAIL></a>
                        — we're happy to help!
                    </p>
                </div>
                <div class="w-full md:w-1/3 text-center md:text-right mb-6 md:mb-0">
                    <img src="data:image/png;base64,@Images.ComputerMousePresent" alt="Computer mouse holding present" class="inline-block"/>
                </div>
            </div>
            <div class="flex items-center justify-end p-5 border-t">
                <button type="button" id="modalFooterNextButton" class="flex items-center justify-center px-5 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 font-medium"
                        hx-get="@Url.Action("ViewStep1", "Register")"
                        hx-target="#registerModalContent"
                        hx-swap="outerHTML">
                    <span class="button-text">Next</span>
                    <svg class="htmx-indicator animate-spin ml-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .htmx-request .button-text {
        display: none;
    }
    .htmx-indicator {
        display: none;
    }
    .htmx-request .htmx-indicator {
        display: inline-block;
    }
    
    /* Breadcrumb Styles */
    #registerModalBreadcrumb {
        margin-bottom: 0.5rem;
    }
    
    /* Future Step (default) */
    .step-circle {
        border-color: #D1D5DB; /* gray-300 */
        background-color: #F3F4F6; /* gray-100 */
        color: #9CA3AF; /* gray-400 */
    }
    .step-label {
        color: #9CA3AF; /* gray-400 */
    }
    .step-connector {
        background-color: #E5E7EB; /* gray-200 */
    }
    
    /* Active Step */
    .step-circle.active {
        border-color: #3B82F6; /* blue-500 */
        background-color: #3B82F6; /* blue-500 */
        color: white;
    }
    .step-label.active {
        color: #2563EB; /* blue-600 */
    }
    
    /* Completed Step */
    .step-circle.completed {
        border-color: #10B981; /* green-500 */
        background-color: #10B981; /* green-500 */
        color: white;
    }
    .step-connector.completed {
        background-color: #10B981; /* green-500 */
    }
    .step-label.completed {
        color: #6B7280; /* gray-500 */
    }
</style>

<script src="https://unpkg.com/htmx.org@1.9.10/dist/htmx.min.js"></script>

<script>
    // Global namespace to avoid variable conflicts across HTMX-loaded content
    window.RegisterModal = window.RegisterModal || {
        // State management
        initialModalContentOuterHTML: '',
        initialModalFooterNextButtonOuterHTML: '',
        
        // Authentication state (from Step1)
        auth: {
            popupWindow: null,
            inProgress: false,
            messageListener: null,
            popupMonitorInterval: null,
            currentProvider: null
        },
        
        // School management state (from Yes/No Existing Relationship)
        schools: {
            addedSchools: [],
            tableContainer: null,
            tableBody: null,
            noSchoolsMessage: null,
            schoolCountMessage: null,
            addSchoolForm: null,
            addSchoolButton: null,
            // Form fields
            positionInput: null,
            schoolNameInput: null,
            schoolAddressInput: null,
            schoolPhoneNumberInput: null,
            requestedAdminCheckbox: null,
            schoolFieldsContainer: null,
            adminCheckboxContainer: null,
            // Error elements
            positionError: null,
            schoolNameError: null,
            schoolAddressError: null,
            schoolPhoneNumberError: null
        },
        
        // Cleanup function to reset all state
        cleanup: function() {
            // Reset authentication state
            this.auth.inProgress = false;
            this.auth.currentProvider = null;
            if (this.auth.popupWindow && !this.auth.popupWindow.closed) {
                this.auth.popupWindow.close();
            }
            this.auth.popupWindow = null;
            if (this.auth.popupMonitorInterval) {
                clearInterval(this.auth.popupMonitorInterval);
                this.auth.popupMonitorInterval = null;
            }
            if (this.auth.messageListener) {
                window.removeEventListener('message', this.auth.messageListener);
                this.auth.messageListener = null;
            }
            
            // Reset school management state
            this.schools.addedSchools = [];
            this.schools.tableContainer = null;
            this.schools.tableBody = null;
            this.schools.noSchoolsMessage = null;
            this.schools.schoolCountMessage = null;
            this.schools.addSchoolForm = null;
            this.schools.addSchoolButton = null;
            this.schools.positionInput = null;
            this.schools.schoolNameInput = null;
            this.schools.schoolAddressInput = null;
            this.schools.schoolPhoneNumberInput = null;
            this.schools.requestedAdminCheckbox = null;
            this.schools.schoolFieldsContainer = null;
            this.schools.adminCheckboxContainer = null;
            this.schools.positionError = null;
            this.schools.schoolNameError = null;
            this.schools.schoolAddressError = null;
            this.schools.schoolPhoneNumberError = null;
        }
    };

    let initialModalContentOuterHTML = '';
    let initialModalFooterNextButtonOuterHTML = '';

    document.addEventListener('DOMContentLoaded', function () {        
        // Override the default behaviour of the Register button to display our modal
        const registerButton = document.querySelector('.ico-register');
        if(!registerButton) {
            return;
        }
        
        registerButton.setAttribute('href', 'javascript:openRegisterModal();');
        
        // Add click event listener to modal backdrop for closing when clicking outside
        const modal = document.getElementById('registerModal');
        if (modal) {
            modal.addEventListener('click', function(event) {
                // Close modal only if the click is directly on the backdrop (not on modal content)
                if (event.target === modal) {
                    closeRegisterModal();
                }
            });
        }
        
        // Store the initial modal content HTML
        const modalContentElement = document.getElementById('registerModalContent');
        if (modalContentElement) {
            initialModalContentOuterHTML = modalContentElement.outerHTML;
        }
        
        // Store the initial Next button HTML
        const modalFooterNextButton = document.getElementById('modalFooterNextButton');
        if (modalFooterNextButton) {
            initialModalFooterNextButtonOuterHTML = modalFooterNextButton.outerHTML;
        }
    });

    function resetModalFooterNextButton() {
        const currentNextButton = document.getElementById('modalFooterNextButton');
        if (currentNextButton && initialModalFooterNextButtonOuterHTML) {
            // Replace the current Next button with the initial one
            // This ensures that if HTMX attributes were modified, we revert to the original.
            currentNextButton.insertAdjacentHTML('beforebegin', initialModalFooterNextButtonOuterHTML);
            currentNextButton.remove();
            
            // Re-process HTMX attributes on the newly inserted button
            const newNextButton = document.getElementById('modalFooterNextButton');
            if (newNextButton) {
                htmx.process(newNextButton);
            }
        }
    }

    function openRegisterModal() {
        const modal = document.getElementById('registerModal');
        if (!modal) {
            console.error('Register modal is null');
            return;
        }

        // Clean up any previous state to avoid conflicts
        window.RegisterModal.cleanup();

        const currentModalContent = document.getElementById('registerModalContent');
        if (currentModalContent && initialModalContentOuterHTML) {
            // Replace the current #registerModalContent with the initial one
            // This ensures that if HTMX swapped it, we revert to the original.
            currentModalContent.insertAdjacentHTML('beforebegin', initialModalContentOuterHTML);
            currentModalContent.remove();
            
            // Re-process HTMX attributes on the newly inserted content, if any
            const newModalContent = document.getElementById('registerModalContent');
            if (newModalContent) {
                 htmx.process(newModalContent);
            }
        }
        
        // Reset the Next button to its original state
        resetModalFooterNextButton();
        
        setModalFooterNextButtonEnabled(true); // Enable 'Next' button for the initial view
        modal.style.display = ''; // Clear inline style
        modal.classList.remove('hidden');
        
        // Initialise the breadcrumb to its default state
        initialiseModalBreadcrumb();
    }

    function setModalFooterNextButtonEnabled(isEnabled) {
        const nextButton = document.getElementById('modalFooterNextButton');
        if(!nextButton) {
            console.error('Next button is null');
            return;
        }
        
        nextButton.disabled = !isEnabled;
        if (!isEnabled) {
            nextButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            nextButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    function closeRegisterModal() {
        const modal = document.getElementById('registerModal');
        if (!modal) {
            console.error('Register modal is null');
            return;
        }
        
        modal.style.display = 'none'; // Add inline style to prevent FOUC
        modal.classList.add('hidden');
        
        // Reset breadcrumb when modal is closed
        initialiseModalBreadcrumb();
    }
    
    /**
     * Initialises the breadcrumb navigation to its default state
     * All steps are set to "future" state
     */
    function initialiseModalBreadcrumb() {
        const breadcrumb = document.getElementById('registerModalBreadcrumb');
        if (!breadcrumb) {
            console.error('registerModalBreadcrumb not found');
            return;
        }
        
        // Reset all steps to future state
        const steps = breadcrumb.querySelectorAll('.step-circle');
        const labels = breadcrumb.querySelectorAll('.step-label');
        const connectors = breadcrumb.querySelectorAll('.step-connector');
        const stepNumbers = breadcrumb.querySelectorAll('.step-number');
        const stepChecks = breadcrumb.querySelectorAll('.step-check');
        
        steps.forEach(step => {
            step.classList.remove('active', 'completed');
        });
        
        labels.forEach(label => {
            label.classList.remove('active', 'completed');
        });
        
        connectors.forEach(connector => {
            connector.classList.remove('completed');
        });
        
        stepNumbers.forEach(number => {
            number.classList.remove('hidden');
        });
        
        stepChecks.forEach(check => {
            check.classList.add('hidden');
        });
    }
    
    /**
     * Updates the breadcrumb navigation to reflect the current step
     * @@param {number} activeStep - The current active step (1, 2, or 3)
     */
    function updateModalBreadcrumb(activeStep) {
        const breadcrumb = document.getElementById('registerModalBreadcrumb');
        if (!breadcrumb) {
            console.error('registerModalBreadcrumb not found');
            return;
        }
        
        // First, initialise all steps to future state
        initialiseModalBreadcrumb();
        
        // Convert activeStep to number to ensure proper comparison
        activeStep = parseInt(activeStep);
        if (isNaN(activeStep) || activeStep < 1 || activeStep > 3) {
            console.error('Invalid step number:', activeStep);
            return;
        }
        
        // Update steps based on active step
        const steps = breadcrumb.querySelectorAll('.step-circle');
        const labels = breadcrumb.querySelectorAll('.step-label');
        const connectors = breadcrumb.querySelectorAll('.step-connector');
        const stepNumbers = breadcrumb.querySelectorAll('.step-number');
        const stepChecks = breadcrumb.querySelectorAll('.step-check');
        
        // Update each step
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            const label = labels[index];
            
            if (stepNumber < activeStep) {
                // Completed step
                step.classList.add('completed');
                label.classList.add('completed');
                
                // Show checkmark instead of number
                const stepNumberEl = step.querySelector('.step-number');
                const stepCheckEl = step.querySelector('.step-check');
                if (stepNumberEl) stepNumberEl.classList.add('hidden');
                if (stepCheckEl) stepCheckEl.classList.remove('hidden');
                
                // Complete the connector to the right of this step
                if (index < connectors.length) {
                    connectors[index].classList.add('completed');
                }
            } else if (stepNumber === activeStep) {
                // Active step
                step.classList.add('active');
                label.classList.add('active');
            }
        });
    }
</script>
