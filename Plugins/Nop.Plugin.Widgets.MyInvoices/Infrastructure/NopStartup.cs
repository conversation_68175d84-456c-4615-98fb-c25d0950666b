using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Nop.Core.Infrastructure;
using Nop.Plugin.Misc.EdunetCore.Services;
using Nop.Plugin.Widgets.MyInvoices.Services;

namespace Nop.Plugin.Widgets.MyInvoices.Infrastructure;

public class NopStartup : INopStartup
{
    public void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // Use shared Nop API HTTP client from EdunetCore plugin
        // The INopApiHttpClient is already registered in EdunetCore's NopStartup
        // We create an adapter to maintain compatibility with existing IInvoiceApiClient interface
        services.AddScoped<IInvoiceApiClient>(provider => 
            new InvoiceApiClientAdapter(provider.GetRequiredService<INopApiHttpClient>()));

        // Register domain services
        services.AddScoped<IInvoiceService, InvoiceService>();
        services.AddScoped<IInvoiceSecurityService, InvoiceSecurityService>();
        services.AddScoped<IInvoiceTransformationService, InvoiceTransformationService>();
    }

    public void Configure(IApplicationBuilder application)
    {
        // No additional configuration needed
    }

    public int Order => 3000;
}