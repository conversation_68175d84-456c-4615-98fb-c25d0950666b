// ViewToggle object for managing invoice view preferences
const ViewToggle = {
    STORAGE_KEY: 'invoice_view_preference',
    isTransitioning: false,
    
    init: function() {
        // Initialise view toggle functionality
        const savedView = this.getPreference();
        this.setView(savedView, false); // No animation on initial load
        this.attachEventListeners();
    },
    
    getPreference: function() {
        try {
            return localStorage.getItem(this.STORAGE_KEY) || 'card';
        } catch (e) {
            return 'card';
        }
    },
    
    savePreference: function(view) {
        try {
            localStorage.setItem(this.STORAGE_KEY, view);
        } catch (e) {
            // Silently fail if localStorage is not available
        }
    },
    
    attachEventListeners: function() {
        const self = this;
        const buttons = document.querySelectorAll('.view-toggle-btn');
        
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                if (!self.isTransitioning) {
                    const view = this.getAttribute('data-view');
                    self.transitionToView(view);
                }
            });
        });
    },
    
    transitionToView: function(view) {
        this.isTransitioning = true;
        const currentView = this.getCurrentView();
        
        if (currentView === view) {
            this.isTransitioning = false;
            return;
        }
        
        const currentContainer = document.getElementById(currentView + 'ViewContainer');
        const newContainer = document.getElementById(view + 'ViewContainer');
        
        // Add fade-out class to current container
        if (currentContainer) {
            currentContainer.classList.add('view-transitioning-out');
        }
        
        // After animation, switch views
        setTimeout(() => {
            if (currentContainer) {
                currentContainer.classList.remove('view-active', 'view-transitioning-out');
            }
            
            if (newContainer) {
                newContainer.classList.add('view-active', 'view-transitioning-in');
                
                // Remove transitioning class after animation
                setTimeout(() => {
                    newContainer.classList.remove('view-transitioning-in');
                    this.isTransitioning = false;
                }, 300);
            }
            
            this.updateButtons(view);
            this.savePreference(view);
        }, 300);
    },
    
    setView: function(view, animate = true) {
        if (animate) {
            this.transitionToView(view);
        } else {
            // Instant switch for initial load
            const containers = document.querySelectorAll('.invoice-view-container');
            containers.forEach(container => {
                container.classList.remove('view-active');
            });
            
            const targetContainer = document.getElementById(view + 'ViewContainer');
            if (targetContainer) {
                targetContainer.classList.add('view-active');
            }
            
            this.updateButtons(view);
        }
    },
    
    getCurrentView: function() {
        const activeContainer = document.querySelector('.invoice-view-container.view-active');
        if (activeContainer) {
            if (activeContainer.id === 'cardViewContainer') {
                return 'card';
            }
            if (activeContainer.id === 'tableViewContainer') {
                return 'table';
            }
        }
        return 'card';
    },
    
    updateButtons: function(view) {
        const buttons = document.querySelectorAll('.view-toggle-btn');
        buttons.forEach(button => {
            const buttonView = button.getAttribute('data-view');
            if (buttonView === view) {
                button.classList.add('active');
                button.setAttribute('aria-pressed', 'true');
            } else {
                button.classList.remove('active');
                button.setAttribute('aria-pressed', 'false');
            }
        });
    }
};

function ready(fn) {
    if (document.readyState !== 'loading') {
        fn();
    } else {
        document.addEventListener('DOMContentLoaded', fn);
    }
}

ready(function() {
    // Initialise ViewToggle
    ViewToggle.init();
    
    // Scroll down to header menu
    const elementToScrollTo = document.querySelector('.search-box');
    if (elementToScrollTo) {
        elementToScrollTo.scrollIntoView({ behavior: "smooth" });
    }
});