using System;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Nop.Core;
using Nop.Core.Http.Extensions;
using Nop.Services.Configuration;
using Nop.Web.Framework.Components;
using Newtonsoft.Json;

namespace Nop.Plugin.ExternalAuth.MultiSSO.Components;

public class School
{
    public string Role { get; set; }
    public string Name { get; set; }
    public string Address { get; set; }
    public string PhoneNumber { get; set; }
    public string LinkedTo { get; set; }
    public bool IsAdmin { get; set; }
    public bool RequestedAdmin { get; set; }
    public string CrmContactId { get; set; }
}

public class ExternalAuthMultiSSOViewComponent(IHttpContextAccessor httpContextAccessor) : NopViewComponent
{
    private const string LOGIN_PATH = "/Login";
    private const string SCHOOL_SESSION_PREFIX = "School:";

    public IViewComponentResult Invoke()
    {
        var requestPath = Request.Path.Value ?? string.Empty;
        var isRequestForLogin = requestPath.Trim().StartsWith(LOGIN_PATH.Trim(), StringComparison.CurrentCultureIgnoreCase);
        
        // Check if user is invited and get school information
        var (isInvitedUser, schoolName) = GetInviteInfo();
        
        // Create a model to pass both login context and invite status
        var model = new PublicInfoModel
        {
            IsLoginRequest = isRequestForLogin,
            IsInvitedUser = isInvitedUser,
            SchoolName = schoolName
        };
        
        return View("~/Plugins/ExternalAuth.MultiSSO/Views/PublicInfo.cshtml", model);
    }
    
    private (bool IsInvited, string SchoolName) GetInviteInfo()
    {
        var session = httpContextAccessor.HttpContext?.Session;
        if (session == null)
        {
            return (false, null);
        }
        
        // Look for school session data
        var sessionKeys = session.Keys;
        var schoolSessionKey = sessionKeys.FirstOrDefault(key => key.StartsWith(SCHOOL_SESSION_PREFIX, StringComparison.OrdinalIgnoreCase));
        
        if (string.IsNullOrEmpty(schoolSessionKey))
        {
            return (false, null);
        }
        
        try
        {
            // Extract school data from session
            var schoolData = session.Get<School>(schoolSessionKey);
            return (true, schoolData?.Name ?? "your school");
        }
        catch
        {
            // If we can't parse the school data, still indicate they're invited
            return (true, "your school");
        }
    }
}

public class PublicInfoModel
{
    public bool IsLoginRequest { get; set; }
    public bool IsInvitedUser { get; set; }
    public string SchoolName { get; set; }
}