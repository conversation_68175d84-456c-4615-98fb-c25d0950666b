<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Copyright>Copyright © SolutionOne</Copyright>
    <Company>SolutionOne</Company>
    <Authors>SolutionOne</Authors>
    <PackageLicenseUrl></PackageLicenseUrl>
    <PackageProjectUrl>https://www.nopcommerce.com/</PackageProjectUrl>
    <RepositoryUrl>https://github.com/nopSolutions/nopCommerce</RepositoryUrl>
    <RepositoryType>Git</RepositoryType>
    <OutputPath>..\..\Presentation\Nop.Web\Plugins\Misc.PublishCategoriesToDatafeed</OutputPath>
    <OutDir>$(OutputPath)</OutDir>
    <!--Set this parameter to true to get the dlls copied from the NuGet cache to the output of your project.
    You need to set this parameter to true if your plugin has a nuget package 
    to ensure that the dlls copied from the NuGet cache to the output of your project-->
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <OutputType>Library</OutputType>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Libraries\Nop.Core\Nop.Core.csproj" />
    <ProjectReference Include="..\..\Libraries\Nop.Services\Nop.Services.csproj" />
    <ProjectReference Include="..\..\Presentation\Nop.Web.Framework\Nop.Web.Framework.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="plugin.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <!-- This target execute after "Build" target -->
  <Target Name="NopTarget" AfterTargets="Build">
    <!-- Delete unnecessary libraries from plugins path -->
    <MSBuild Projects="@(ClearPluginAssemblies)" Properties="PluginPath=$(MSBuildProjectDirectory)\$(OutDir)" Targets="NopClear" />
  </Target>
</Project>