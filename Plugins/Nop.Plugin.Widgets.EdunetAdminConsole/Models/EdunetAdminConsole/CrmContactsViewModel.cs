using System.Collections.Generic;
using Nop.Plugin.Misc.EdunetCore.Models.CrmApi;

namespace Nop.Plugin.Widgets.EdunetAdminConsole.Models.EdunetAdminConsole
{
    public class SchoolSelectionViewModel
    {
        public string Name { get; set; }
        public int OriginalIndex { get; set; } // Index in the customer's school attribute list
        public bool IsLinked { get; set; }
        public string LinkedCrmContactId { get; set; } // The CRM Contact ID this school is currently linked to, if any
        public string LinkedToCrmAccountId { get; set; } // The CRM Account ID portion from LinkedTo field, if any
    }

    public class CrmContactsViewModel
    {
        public LookupContactsWithNameResponse CrmContactsResponse { get; set; }
        public List<SchoolSelectionViewModel> TargetCustomerSchools { get; set; } = [];
        public int TargetCustomerId { get; set; }
        public string ErrorMessage { get; set; } // General error for loading data for this view model
    }
}
