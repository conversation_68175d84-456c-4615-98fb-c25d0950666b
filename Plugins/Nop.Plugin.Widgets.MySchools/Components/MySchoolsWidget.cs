using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Nop.Core;
using Nop.Plugin.Widgets.MySchools.Models;
using Nop.Web.Framework.Components;
using Nop.Web.Models.Customer;

namespace Nop.Plugin.Widgets.MySchools.Components;

public class MySchoolsWidget : NopViewComponent
{
    public const int MySchoolsTabIndex = 750;
    private const string MY_SCHOOLS_WIDGET_VIEW = "~/Plugins/Widgets.MySchools/Views/MySchoolsWidget.cshtml";

    public Task<IViewComponentResult> InvokeAsync(string widgetZone, object additionalData)
    {
        if (additionalData is not CustomerNavigationModel model)
        {
            return Task.FromResult<IViewComponentResult>(View(MY_SCHOOLS_WIDGET_VIEW, new MySchoolsWidgetModel
            {
                IsValid = false,
                LinkActive = false
            }));
        }

        var linkActive = model.SelectedTab == MySchoolsTabIndex;
        return Task.FromResult<IViewComponentResult>(View(MY_SCHOOLS_WIDGET_VIEW, new MySchoolsWidgetModel
        {
            IsValid = true,
            LinkActive = linkActive
        }));
    }
}