@model CustomerTokenEditModel

@{
    Layout = "_ColumnsTwo";
    NopHtml.AddTitleParts(T("Plugins.Payments.CyberSource.PaymentTokens").Text);
    NopHtml.AppendPageCssClassParts("html-account-page");
}

@section left
{
    @(await Component.InvokeAsync(typeof(CustomerNavigationViewComponent), new { selectedTabId = CyberSourceDefaults.CustomerTokensMenuTab }))
}

<form asp-route="@CyberSourceDefaults.CustomerTokenAddRouteName" method="post">
    <div class="page account-page address-edit-page">
        <div class="page-title">
            <h1>@T("Account.MyAccount") - @T("Plugins.Payments.CyberSource.PaymentTokens.AddNew")</h1>
        </div>
        <div class="page-body">
            <div asp-validation-summary="ModelOnly" class="message-error"></div>
            @{
                var dataDictAddress = new ViewDataDictionary(ViewData);
                dataDictAddress.TemplateInfo.HtmlFieldPrefix = "CustomerToken";
                @await Html.PartialAsync("~/Plugins/Payments.CyberSource/Views/CustomerToken/_CreateOrUpdateToken.cshtml", Model.CustomerToken, dataDictAddress)
            }
            <div class="buttons">
                <button type="submit" class="button-1 save-address-button">@T("Common.Save")</button>
            </div>
        </div>
    </div>
</form>