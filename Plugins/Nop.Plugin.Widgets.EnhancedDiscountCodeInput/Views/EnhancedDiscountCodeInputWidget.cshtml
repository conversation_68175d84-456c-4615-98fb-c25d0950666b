@model Nop.Plugin.Widgets.EnhancedDiscountCodeInput.Models.EnhancedDiscountCodeInputWidgetModel
@{
    Layout = "";
}

@if (Model.IsValid && Model.DiscountBoxModel is { Display: true })
{
    <!-- Add CSS reference -->
    <link rel="stylesheet" href="~/Plugins/Widgets.EnhancedDiscountCodeInput/wwwroot/css/enhanced-discount.css"
          asp-append-version="true"/>

    <!-- Enhanced Discount Component -->
    <div class="enhanced-discount-component coupon-box" id="enhancedDiscountBox">
        <!-- Header -->
        <div class="enhanced-discount-header title">
            <strong>@T("ShoppingCart.DiscountCouponCode")</strong>
        </div>

        <!-- Input Group -->
        <div class="enhanced-input-group coupon-code">
            <input
                type="text"
                id="enhancedDiscountCode"
                name="discountcouponcode"
                class="enhanced-discount-input discount-coupon-code"
                aria-label="@T("ShoppingCart.DiscountCouponCode.Label")"
            >
            <button
                type="submit"
                id="enhancedApplyDiscount"
                name="applydiscountcouponcode"
                class="enhanced-apply-button button-2 apply-discount-coupon-code-button"
            >
                @T("ShoppingCart.DiscountCouponCode.Button")
            </button>
        </div>

        <!-- Hint -->
        <div class="enhanced-hint-text hint">@T("ShoppingCart.DiscountCouponCode.Tooltip")</div>

        <!-- Messages -->
        @foreach (var message in Model.DiscountBoxModel.Messages)
        {
            <div
                class="enhanced-message @(Model.DiscountBoxModel.IsApplied ? "enhanced-success-message message-success" : "enhanced-error-message message-failure") show"
                id="discountMessage">
                @message
            </div>
        }

        <!-- Applied Discounts -->
        @foreach (var enhancedDiscount in Model.EnhancedDiscounts)
        {
            <div class="enhanced-applied-code current-code show" data-discount-id="@enhancedDiscount.Id">
                <span class="applied-discount-code">@string.Format(T("ShoppingCart.DiscountCouponCode.CurrentCode").Text, enhancedDiscount.CouponCode)</span>
                @if (!string.IsNullOrEmpty(enhancedDiscount.DiscountAmountDisplay))
                {
                    <span class="enhanced-discount-amount">(@enhancedDiscount.DiscountAmountDisplay)</span>
                }
                <button
                    type="submit"
                    name="<EMAIL>"
                    class="enhanced-remove-button remove-discount-button"
                    aria-label="@T("ShoppingCart.DiscountCouponCode.Remove")"
                >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"
                         stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
        }
    </div>

    <!-- Add JavaScript reference -->
    <script src="~/Plugins/Widgets.EnhancedDiscountCodeInput/wwwroot/js/enhanced-discount.js" asp-append-version="true"
            asp-location="Footer"></script>
}