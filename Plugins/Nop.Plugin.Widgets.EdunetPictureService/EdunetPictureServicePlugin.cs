using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nop.Plugin.Widgets.EdunetPictureService.Components;
using Nop.Services.Cms;
using Nop.Services.Plugins;
using Nop.Web.Framework.Infrastructure;

namespace Nop.Plugin.Widgets.EdunetPictureService;

public class EdunetPictureServicePlugin : BasePlugin, IWidgetPlugin
{
    public bool HideInWidgetList => true;
    public Task<IList<string>> GetWidgetZonesAsync()
    {
        return Task.FromResult<IList<string>>(new List<string>
        {
            PublicWidgetZones.HeadHtmlTag
        });
    }

    public Type GetWidgetViewComponent(string widgetZone)
    {
        return typeof(EdunetPictureServiceWidget);
    }
}