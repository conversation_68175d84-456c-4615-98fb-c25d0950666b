using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Nop.Core;
using Nop.Core.Domain.Catalog;
using Nop.Core.Domain.Media;
using Nop.Plugin.Misc.Datafeed.Models;
using Nop.Services.Catalog;
using Nop.Services.Media;
using Nop.Web.Framework.Controllers;

namespace Nop.Plugin.Misc.Datafeed.Controllers;

public class ImageUpdateController(
    IProductService productService,
    IPictureService pictureService)
    : BasePluginController
{
    private const string API_HEADER = "X-API-KEY";
    private const string API_KEY = "NopImage2024SecureKey!@#DatafeedPluginAccess$%^ProductImageManagement&*(APIEndpoint)";

    [HttpPost]
    public async Task<IActionResult> AddImage([FromBody] AddImageRequest request)
    {
        var authResult = ValidateApiKey();
        if (authResult != null)
        {
            return authResult;
        }

        if (!ModelState.IsValid)
        {
            Response.StatusCode = (int)HttpStatusCode.BadRequest;
            return Json(new ImageUpdateResponse { Error = "Invalid request data" });
        }

        try
        {
            var product = await productService.GetProductBySkuAsync(request.Sku);
            if (product == null)
            {
                Response.StatusCode = (int)HttpStatusCode.NotFound;
                return Json(new ImageUpdateResponse { Error = $"Product with SKU '{request.Sku}' not found" });
            }

            byte[] imageBytes;
            try
            {
                imageBytes = Convert.FromBase64String(request.ImageBase64);
            }
            catch (FormatException)
            {
                Response.StatusCode = (int)HttpStatusCode.BadRequest;
                return Json(new ImageUpdateResponse { Error = "Invalid base64 image data" });
            }

            var mimeType = GetMimeTypeFromBase64(request.ImageBase64);
            var seoFilename = !string.IsNullOrEmpty(request.SeoFilename) 
                ? request.SeoFilename 
                : $"{request.Sku}-image";

            var picture = await pictureService.InsertPictureAsync(
                imageBytes, 
                mimeType, 
                seoFilename, 
                isNew: true, 
                validateBinary: true);

            var productPicture = new ProductPicture
            {
                ProductId = product.Id,
                PictureId = picture.Id,
                DisplayOrder = request.DisplayOrder
            };

            await productService.InsertProductPictureAsync(productPicture);

            return Json(new ImageUpdateResponse 
            { 
                Success = true, 
                Message = "Image added successfully",
                PictureId = picture.Id
            });
        }
        catch (Exception ex)
        {
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Json(new ImageUpdateResponse { Error = $"Failed to add image: {ex.Message}" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> RemoveImage([FromBody] RemoveImageRequest request)
    {
        var authResult = ValidateApiKey();
        if (authResult != null)
        {
            return authResult;
        }

        if (!ModelState.IsValid)
        {
            Response.StatusCode = (int)HttpStatusCode.BadRequest;
            return Json(new ImageUpdateResponse { Error = "Invalid request data" });
        }

        try
        {
            var product = await productService.GetProductBySkuAsync(request.Sku);
            if (product == null)
            {
                Response.StatusCode = (int)HttpStatusCode.NotFound;
                return Json(new ImageUpdateResponse { Error = $"Product with SKU '{request.Sku}' not found" });
            }

            var productPictures = await productService.GetProductPicturesByProductIdAsync(product.Id);
            
            foreach (var productPicture in productPictures)
            {
                var pictures = await pictureService.GetPicturesByProductIdAsync(product.Id);
                var picture = pictures.FirstOrDefault(p => p.Id == productPicture.PictureId);

                if (picture == null)
                {
                    continue;
                }
                
                var pictureBinary = await pictureService.LoadPictureBinaryAsync(picture);
                var pictureChecksum = HashHelper.CreateHash(pictureBinary, "SHA256");

                if (!string.Equals(pictureChecksum, request.ImageSha256Checksum,
                        StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }
                
                await productService.DeleteProductPictureAsync(productPicture);
                await pictureService.DeletePictureAsync(picture);
                        
                return Json(new ImageUpdateResponse 
                { 
                    Success = true, 
                    Message = "Image removed successfully",
                    PictureId = picture.Id
                });
            }

            Response.StatusCode = (int)HttpStatusCode.NotFound;
            return Json(new ImageUpdateResponse { Error = "Image with specified checksum not found for this product" });
        }
        catch (Exception ex)
        {
            Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            return Json(new ImageUpdateResponse { Error = $"Failed to remove image: {ex.Message}" });
        }
    }

    private IActionResult ValidateApiKey()
    {
        if (!Request.Headers.TryGetValue(API_HEADER, out var headerKey))
        {
            Response.StatusCode = (int)HttpStatusCode.Unauthorized;
            return Json(new ImageUpdateResponse { Error = "Missing API key" });
        }

        if (headerKey == API_KEY)
        {
            return null;
        }
        
        Response.StatusCode = (int)HttpStatusCode.Unauthorized;
        return Json(new ImageUpdateResponse { Error = "Invalid API key" });

    }

    private static string GetMimeTypeFromBase64(string base64String)
    {
        if (base64String.StartsWith("/9j/"))
        {
            return "image/jpeg";
        }
        if (base64String.StartsWith("iVBORw0KGgo"))
        {
            return "image/png";
        }
        if (base64String.StartsWith("R0lGOD"))
        {
            return "image/gif";
        }
        if (base64String.StartsWith("UklGR"))
        {
            return "image/webp";
        }
        
        return "image/jpeg";
    }
}