using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using Newtonsoft.Json;
using Nop.Core;
using Nop.Core.Domain.Catalog;
using Nop.Core.Domain.Seo;
using Nop.Core.Domain.Tax;
using Nop.Plugin.Misc.EdunetCore.Services;
using Nop.Services.Catalog;
using Nop.Services.Localization;
using Nop.Services.Logging;
using Nop.Services.Media;
using Nop.Services.Seo;
using Nop.Services.Tax;

namespace Nop.Plugin.Misc.Datafeed.Services;

/// <summary>
/// Handles product synchronisation operations with improved error handling and performance
/// </summary>
public class ProductSyncService : IProductSyncService
{
    private const int PRODUCT_CLEANUP_BATCH_SIZE = 500;

    private readonly IProductService _productService;
    private readonly IPictureService _pictureService;
    private readonly ICategoryService _categoryService;
    private readonly ILocalizationService _localizationService;
    private readonly ICustomerActivityService _customerActivityService;
    private readonly IUrlRecordService _urlRecordService;
    private readonly IProductAttributeService _productAttributeService;
    private readonly ISpecificationAttributeService _specificationAttributeService;
    private readonly ITaxCategoryService _taxCategoryService;
    private readonly IManufacturerService _manufacturerService;
    private readonly ISharedDatafeedApiClient _sharedDatafeedApiClient;

    private static readonly string[] SourceArray =
    [
        "not found",
        "information not available",
        "information not found"
    ];

    /// <summary>
    /// Initialises a new instance of the ProductSyncService class
    /// </summary>
    public ProductSyncService(
        IProductService productService,
        IPictureService pictureService,
        ICategoryService categoryService,
        ILocalizationService localizationService,
        ICustomerActivityService customerActivityService,
        IUrlRecordService urlRecordService,
        IProductAttributeService productAttributeService,
        ISpecificationAttributeService specificationAttributeService,
        ITaxCategoryService taxCategoryService,
        IManufacturerService manufacturerService,
        ISharedDatafeedApiClient sharedDatafeedApiClient)
    {
        _productService = productService;
        _pictureService = pictureService;
        _categoryService = categoryService;
        _localizationService = localizationService;
        _customerActivityService = customerActivityService;
        _urlRecordService = urlRecordService;
        _productAttributeService = productAttributeService;
        _specificationAttributeService = specificationAttributeService;
        _taxCategoryService = taxCategoryService;
        _manufacturerService = manufacturerService;
        _sharedDatafeedApiClient = sharedDatafeedApiClient;
    }

    /// <inheritdoc/>
    public async Task<Result> SyncProductsAsync(DatafeedProductSyncTask.DatafeedCsvFeedModel[] records,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (records == null)
            {
                return Result.Failure("Records array is null");
            }

            // Empty feed is valid - it means we want to remove all products
            if (records.Length == 0)
            {
                await CleanupProductsNotInFeedAsync(records, cancellationToken).ConfigureAwait(false);
                return Result.Success();
            }

            var taxCategory = await GetTaxCategoryAsync().ConfigureAwait(false);
            if (taxCategory == null)
            {
                return Result.Failure("Tax category not found");
            }

            var attributeOptions = await LoadAttributeOptionsAsync().ConfigureAwait(false);
            var allManufacturers = await _manufacturerService.GetAllManufacturersAsync().ConfigureAwait(false);

            foreach (var record in records)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var product = await GetOrCreateProductAsync(record).ConfigureAwait(false);
                    var isExistingProduct = product.Id != default;

                    // If the record indicates the product should not be published
                    if (!record.Published)
                    {
                        // And the product actually exists in the database, delete it
                        if (isExistingProduct)
                        {
                            await _productService.DeleteProductAsync(product).ConfigureAwait(false);
                            await _customerActivityService.InsertActivityAsync(
                                "DeleteProductViaDatafeed",
                                string.Format(await _localizationService.GetResourceAsync("ActivityLog.DeleteProduct").ConfigureAwait(false),
                                    product.Name),
                                product).ConfigureAwait(false);
                        }

                        // Whether it existed or not, if it's not published, skip to the next record
                        continue;
                    }

                    // If the record indicates the product is archived
                    if (record.Archived)
                    {
                        // And the product actually exists in the database, delete it
                        if (isExistingProduct)
                        {
                            await _productService.DeleteProductAsync(product).ConfigureAwait(false);
                            await _customerActivityService.InsertActivityAsync(
                                "DeleteProductViaDatafeedArchived",
                                string.Format(await _localizationService.GetResourceAsync("ActivityLog.DeleteProduct").ConfigureAwait(false),
                                    product.Name),
                                product).ConfigureAwait(false);
                        }

                        // Whether it existed or not, if it's archived, skip to the next record
                        continue;
                    }

                    if (record.SellPriceInc <= 0 || record.CostPrice >=
                        record.SellPriceInc) // We never want to publish products that we would be giving away
                    {
                        await _productService.DeleteProductAsync(product);
                        await _customerActivityService.InsertActivityAsync(
                            "DeleteProductViaDatafeedSellPriceLowerThanCostPrice",
                            string.Format(await _localizationService.GetResourceAsync("ActivityLog.DeleteProduct").ConfigureAwait(false),
                                product.Name),
                            product).ConfigureAwait(false);

                        continue;
                    }

                    // --- Product is Published ---
                    // Update core product details (applies to both new and existing)
                    await UpdateProductDetailsAsync(product, record, taxCategory.Id).ConfigureAwait(false);

                    // Save the product (Insert or Update)
                    if (!isExistingProduct)
                    {
                        await HandleNewProductAsync(product, record).ConfigureAwait(false); // Inserts, logs, creates URL record
                    }
                    else
                    {
                        await _productService.UpdateProductAsync(product).ConfigureAwait(false);
                    }

                    // Update related entities (Manufacturers, Attributes, Categories, Pictures)
                    await UpdateManufacturersAsync(product, record, allManufacturers).ConfigureAwait(false);
                    await UpdateAttributesAsync(product, record, attributeOptions).ConfigureAwait(false);
                    await UpdateCategoriesAsync(product, record).ConfigureAwait(false);
                    await UpdatePicturesAsync(product, record).ConfigureAwait(false);
                }
                catch (OperationCanceledException)
                {
                    throw; // Re-throw cancellation exceptions immediately
                }
                catch (Exception ex)
                {
                    await LogProductSyncFailureAsync(record.Sku, ex.Message).ConfigureAwait(false);
                    // Stop the sync on error for a single record
                    throw new Exception($"Failed to process record for SKU {record.Sku}: {ex.Message}", ex);
                }
            }

            // Clean up products not present in the feed
            await CleanupProductsNotInFeedAsync(records, cancellationToken).ConfigureAwait(false);

            return Result.Success();
        }
        catch (OperationCanceledException)
        {
            return Result.Failure("Product synchronisation was cancelled");
        }
    }

    /// <summary>
    /// Removes products from the database that are not present in the current datafeed.
    /// This method identifies products that exist in the database but are not present in the current feed,
    /// enabling efficient cleanup of obsolete products.
    ///
    /// The method implements a paging loop to process products in batches using SearchProductsAsync to prevent
    /// memory issues when dealing with large product catalogues. Products are retrieved in batches of 500 and
    /// processed sequentially until all products have been examined.
    ///
    /// For each product not found in the feed, the product is deleted and an activity log entry is created.
    /// The operation respects cancellation requests through the provided cancellationToken.
    /// </summary>
    /// <param name="records">Array of datafeed records containing product information</param>
    /// <param name="cancellationToken">Cancellation token to cancel the operation</param>
    /// <exception cref="ArgumentNullException">Thrown when records array is null</exception>
    private async Task CleanupProductsNotInFeedAsync(DatafeedProductSyncTask.DatafeedCsvFeedModel[] records,
        CancellationToken cancellationToken = default)
    {
        if (records == null)
        {
            throw new ArgumentNullException(nameof(records), "Records array cannot be null");
        }

        // Extract SKUs from records using LINQ and create a case-insensitive HashSet for O(1) lookup
        var feedSkus = records
            .Where(record => record != null && !string.IsNullOrWhiteSpace(record.Sku))
            .Select(record => record.Sku)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        // Process products in batches using paging
        var pageIndex = 0;
        IPagedList<Product> products;

        do
        {
            cancellationToken.ThrowIfCancellationRequested();

            products = await _productService.SearchProductsAsync(
                pageIndex: pageIndex,
                pageSize: PRODUCT_CLEANUP_BATCH_SIZE,
                showHidden: true,
                overridePublished: true).ConfigureAwait(false);

            var productsToDelete = products
                .Where(product => string.IsNullOrWhiteSpace(product.Sku) || !feedSkus.Contains(product.Sku)).ToList();

            // Collect products that need to be deleted

            var deletedAnyProducts = false;

            // Batch delete products if any found
            if (productsToDelete.Count != 0)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    await _productService.DeleteProductsAsync(productsToDelete).ConfigureAwait(false);

                    // Log a single batch activity entry for better performance
                    var deletedProductNames = productsToDelete.Take(10).Select(p => p.Name);
                    var productNamesDisplay = string.Join(", ", deletedProductNames);
                    var remainingCount = productsToDelete.Count - 10;
                    var batchMessage = remainingCount > 0
                        ? $"Batch deleted {productsToDelete.Count} products during datafeed cleanup: {productNamesDisplay} and {remainingCount} more"
                        : $"Batch deleted {productsToDelete.Count} products during datafeed cleanup: {productNamesDisplay}";

                    await _customerActivityService.InsertActivityAsync(
                        "DeleteProductViaDatafeedCleanup",
                        batchMessage,
                        null).ConfigureAwait(false);

                    deletedAnyProducts = true;
                }
                catch (Exception ex)
                {
                    // Fall back to individual deletion if batch delete fails
                    foreach (var product in productsToDelete)
                    {
                        try
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            await _productService.DeleteProductAsync(product).ConfigureAwait(false);
                            deletedAnyProducts = true;
                        }
                        catch (Exception individualEx)
                        {
                            await LogProductSyncFailureAsync(product.Sku, $"Cleanup failed: {individualEx.Message}").ConfigureAwait(false);
                            // Continue with next product
                        }
                    }

                    // Log individual fallback activity if any products were processed
                    if (productsToDelete.Count != 0)
                    {
                        await _customerActivityService.InsertActivityAsync(
                            "DeleteProductViaDatafeedCleanup",
                            $"Individual fallback deletion of {productsToDelete.Count} products during datafeed cleanup (batch deletion failed)",
                            null).ConfigureAwait(false);
                    }
                }
            }

            // Only increment page index if we didn't delete any products
            // If we deleted products, the next page has shifted down to become the current page
            if (!deletedAnyProducts)
            {
                pageIndex++;
            }
        } while (products.Any()); // Continue until we get an empty page
    }

    /// <summary>
    /// Retrieves the GST tax category from the database
    /// </summary>
    /// <returns>The GST tax category if found, otherwise null</returns>
    private async Task<TaxCategory> GetTaxCategoryAsync()
    {
        var categories = await _taxCategoryService.GetAllTaxCategoriesAsync().ConfigureAwait(false);
        return categories.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), "GST".Trim(), StringComparison.CurrentCultureIgnoreCase));
    }

    /// <summary>
    /// Gets the list of specification attribute values that should be ignored during synchronisation
    /// </summary>
    /// <returns>Array of strings to ignore when processing specification attributes</returns>
    private static Task<string[]> GetSpecificationAttributeOptionIgnoreListAsync()
    {
        return Task.FromResult(SourceArray.Select(q => q.Trim()).ToArray());
    }

    /// <summary>
    /// Loads all specification attribute groups, their attributes, and available options from the database
    /// </summary>
    /// <returns>A hierarchical structure containing attribute groups with their attributes and options</returns>
    private async
        Task<List<KeyValuePair<SpecificationAttributeGroup,
            KeyValuePair<List<SpecificationAttribute>, List<SpecificationAttributeOption>>>>>
        LoadAttributeOptionsAsync()
    {
        var options =
            new List<KeyValuePair<SpecificationAttributeGroup,
                KeyValuePair<List<SpecificationAttribute>, List<SpecificationAttributeOption>>>>();

        var groups = await _specificationAttributeService.GetSpecificationAttributeGroupsAsync().ConfigureAwait(false);
        foreach (var group in groups.ToList())
        {
            var attributes = await _specificationAttributeService.GetSpecificationAttributesByGroupIdAsync(group.Id).ConfigureAwait(false);
            if (!attributes.Any())
            {
                continue;
            }

            var specOptions = new List<SpecificationAttributeOption>();
            foreach (var attribute in attributes)
            {
                var attrOptions = await _specificationAttributeService
                    .GetSpecificationAttributeOptionsBySpecificationAttributeAsync(attribute.Id).ConfigureAwait(false);
                specOptions.AddRange(attrOptions);
            }

            options.Add(
                new KeyValuePair<SpecificationAttributeGroup,
                    KeyValuePair<List<SpecificationAttribute>, List<SpecificationAttributeOption>>>(
                    group,
                    new KeyValuePair<List<SpecificationAttribute>, List<SpecificationAttributeOption>>(
                        attributes.ToList(),
                        specOptions)));
        }

        return options;
    }

    /// <summary>
    /// Retrieves an existing product by SKU or creates a new product instance
    /// </summary>
    /// <param name="record">The datafeed record containing product information</param>
    /// <returns>An existing product if found by SKU, otherwise a new product instance</returns>
    private async Task<Product> GetOrCreateProductAsync(DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        return await _productService.GetProductBySkuAsync(record.Sku).ConfigureAwait(false) ?? new Product
        {
            CreatedOnUtc = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Updates the core product details from datafeed record values
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing new product information</param>
    /// <param name="taxCategoryId">The ID of the tax category to assign to the product</param>
    /// <returns>A completed task</returns>
    private static Task UpdateProductDetailsAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record,
        int taxCategoryId)
    {
        product.UpdatedOnUtc = DateTime.UtcNow;
        product.Sku = record.Sku;
        product.VisibleIndividually = true;
        product.Name = record.Name.Length > 400 ? record.Name[..400] : record.Name;
        product.ShortDescription = record.ShortDescription;
        product.FullDescription = record.LongDescription;
        // product.ShowOnHomepage = false;
        product.StockQuantity = record.QtyAvailable;
        product.DisplayStockQuantity = true;
        product.DisplayStockAvailability = true;
        product.LowStockActivity = LowStockActivity.DisableBuyButton;
        product.OrderMinimumQuantity = 1;
        product.OrderMaximumQuantity = 1000;
        product.ManageInventoryMethodId = (int)ManageInventoryMethod.ManageStock;
        product.Price = record.SellPriceInc;
        product.ProductCost = record.CostPrice;
        product.Published = true;
        product.ProductTypeId = (int)ProductType.SimpleProduct;
        product.BasepriceUnitId = 1;
        product.BasepriceBaseUnitId = 1;
        product.TaxCategoryId = taxCategoryId;
        product.IsShipEnabled = true;
        product.BackorderModeId =
            (int)BackorderMode
                .AllowQtyBelow0; // This allows the user to add to cart (and thus generate quote) even for items that are out of stock
        return Task.CompletedTask;
    }

    /// <summary>
    /// Handles the insertion of a new product including stock history, activity logging, and URL record creation
    /// </summary>
    /// <param name="product">The new product to insert</param>
    /// <param name="record">The datafeed record containing product information</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task HandleNewProductAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        await _productService.InsertProductAsync(product).ConfigureAwait(false);

        await _productService.AddStockQuantityHistoryEntryAsync(
            product,
            product.StockQuantity,
            product.StockQuantity,
            product.WarehouseId,
            await _localizationService.GetResourceAsync("Admin.StockQuantityHistory.Messages.Edit").ConfigureAwait(false)
        ).ConfigureAwait(false);

        await _customerActivityService.InsertActivityAsync(
            "AddNewProduct",
            string.Format(await _localizationService.GetResourceAsync("ActivityLog.AddNewProduct").ConfigureAwait(false),
                product.Name),
            product
        ).ConfigureAwait(false);

        await _urlRecordService.InsertUrlRecordAsync(new UrlRecord
        {
            EntityId = product.Id,
            EntityName = "Product",
            IsActive = true,
            Slug = await _urlRecordService.ValidateSeNameAsync(product, string.Empty, product.Name, true).ConfigureAwait(false),
        }).ConfigureAwait(false);
    }

    /// <summary>
    /// Updates the manufacturer associations for a product
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing manufacturer information</param>
    /// <param name="allManufacturers">List of all available manufacturers in the system</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdateManufacturersAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record,
        IList<Manufacturer> allManufacturers)
    {
        var existing = await _manufacturerService.GetProductManufacturersByProductIdAsync(product.Id).ConfigureAwait(false);
        foreach (var existingManufacturer in existing)
        {
            await _manufacturerService.DeleteProductManufacturerAsync(existingManufacturer).ConfigureAwait(false);
        }

        var manufacturer = allManufacturers.SingleOrDefault(q =>
            string.Equals(q.Name.Trim(), record.Brand.Trim(),
                StringComparison.CurrentCultureIgnoreCase));

        if (manufacturer != null)
        {
            await _manufacturerService.InsertProductManufacturerAsync(new ProductManufacturer
            {
                ProductId = product.Id,
                ManufacturerId = manufacturer.Id
            }).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Updates the specification attributes for a product based on datafeed information
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing attribute information</param>
    /// <param name="attributeOptions">Pre-loaded attribute groups, attributes, and options</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdateAttributesAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record,
        List<KeyValuePair<SpecificationAttributeGroup,
            KeyValuePair<List<SpecificationAttribute>, List<SpecificationAttributeOption>>>> attributeOptions)
    {
        if (string.IsNullOrWhiteSpace(record.JsonFeatures) && string.IsNullOrWhiteSpace(record.JsonSpecifications))
        {
            return;
        }

        var ignoreList = await GetSpecificationAttributeOptionIgnoreListAsync().ConfigureAwait(false);

        var existing = await _specificationAttributeService.GetProductSpecificationAttributesAsync(product.Id).ConfigureAwait(false);
        foreach (var existingAttribute in existing)
        {
            await _specificationAttributeService.DeleteProductSpecificationAttributeAsync(existingAttribute).ConfigureAwait(false);
        }

        List<DatafeedProductSyncTask.ProductAttributeModel> parsedAttributeNames;
        try
        {
            parsedAttributeNames =
                JsonConvert.DeserializeObject<List<DatafeedProductSyncTask.ProductAttributeModel>>(
                    record.JsonSpecifications);
            if (parsedAttributeNames == null) // Handle case where deserialisation results in null but doesn't throw
            {
                // Or return, or throw a specific exception if null is invalid
                parsedAttributeNames = [];
            }
        }
        catch (JsonException) // Catch specific JSON exceptions
        {
            // Re-throw the original exception to preserve its type
            throw;
        }

        var category = await _categoryService.GetCategoryByIdAsync((int)record.NopCategoryId).ConfigureAwait(false);

        var specAttributeKvp = attributeOptions.SingleOrDefault(q =>
                string.Equals(q.Key.Name.Trim(), category.Name.Trim(),
                    StringComparison.CurrentCultureIgnoreCase))
            .Value;

        foreach (var attributeKvp in specAttributeKvp.Key ?? [])
        {
            var parsedAttribute = parsedAttributeNames.SingleOrDefault(q =>
                q.Value.Split(":").First().Trim() == attributeKvp.Name.Trim());
            if (parsedAttribute == null)
            {
                continue;
            }

            var optionName = parsedAttribute.Value.Split(":").Last().Trim();
            if (ignoreList.Contains(optionName, StringComparer.OrdinalIgnoreCase))
            {
                continue;
            }

            var option = specAttributeKvp.Value.SingleOrDefault(q =>
                string.Equals(q.Name.Trim(), optionName, StringComparison.CurrentCultureIgnoreCase));

            if (option == null)
            {
                option = new SpecificationAttributeOption
                {
                    SpecificationAttributeId = attributeKvp.Id,
                    Name = optionName,
                };

                await _specificationAttributeService.InsertSpecificationAttributeOptionAsync(option).ConfigureAwait(false);
                specAttributeKvp.Value.Add(option);
            }

            var attributeMapping = new ProductSpecificationAttribute
            {
                ShowOnProductPage = true,
                AllowFiltering = true,
                SpecificationAttributeOptionId = option.Id,
                ProductId = product.Id,
                AttributeTypeId = option.SpecificationAttributeId,
                AttributeType = SpecificationAttributeType.CustomText,
                CustomValue = optionName,
            };
            await _specificationAttributeService.InsertProductSpecificationAttributeAsync(attributeMapping).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Updates the category associations for a product
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing category information</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdateCategoriesAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        var existing = await _categoryService.GetProductCategoriesByProductIdAsync(product.Id).ConfigureAwait(false);
        // Don't delete the product-category relationship because it resets any ordering that was applied
        foreach (var productCategory in existing)
        {
            // await _categoryService.DeleteProductCategoryAsync(productCategory);
        }

        // Only insert a new relationship if none exist
        if (!existing.Any())
        {
            await _categoryService.InsertProductCategoryAsync(new ProductCategory
            {
                CategoryId = (int)record.NopCategoryId,
                ProductId = product.Id
            }).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Updates product pictures from the product source API
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing product source information</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdateProductSourcePicturesAsync(Product product,
        DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        var pictures = await _pictureService.GetPicturesByProductIdAsync(product.Id).ConfigureAwait(false);
        if (pictures.Any() || record.ProductSourceId <= 0)
        {
            return;
        }

        var picturesResult = await _sharedDatafeedApiClient.FetchProductSourceImagesAsync(record.ProductSourceId).ConfigureAwait(false);
        if (!picturesResult.IsSuccess)
        {
            return;
        }

        foreach (var productSourceImages in picturesResult.Value.Images.Where(img =>
                     !string.IsNullOrEmpty(img.ImageBytes)))
        {
            var picture = await _pictureService.InsertPictureAsync(
                Convert.FromBase64String(productSourceImages.ImageBytes),
                "image/png",
                record.Sku.Replace("/", ""),
                record.Sku
            ).ConfigureAwait(false);

            await _productService.InsertProductPictureAsync(new ProductPicture
            {
                PictureId = picture.Id,
                DisplayOrder = 1,
                ProductId = product.Id
            }).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Updates product pictures from the price book API
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing price book information</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdatePriceBookPicturesAsync(Product product,
        DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        var pictures = await _pictureService.GetPicturesByProductIdAsync(product.Id).ConfigureAwait(false);
        if (pictures.Any() || record.PriceBookItemId <= 0)
        {
            return;
        }

        var picturesResult = await _sharedDatafeedApiClient.FetchPriceBookProductImagesAsync(record.PriceBookItemId).ConfigureAwait(false);
        if (!picturesResult.IsSuccess)
        {
            return;
        }

        foreach (var priceBookProductImage in picturesResult.Value.Images)
        {
            var picture = await _pictureService.InsertPictureAsync(
                Convert.FromBase64String(priceBookProductImage.ImageBytes),
                "image/png",
                record.Sku.Replace("/", ""),
                record.Sku
            ).ConfigureAwait(false);

            await _productService.InsertProductPictureAsync(new ProductPicture
            {
                PictureId = picture.Id,
                DisplayOrder = 1,
                ProductId = product.Id
            }).ConfigureAwait(false);
        }
    }

    /// <summary>
    /// Updates product pictures from either product source or price book based on record data
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <param name="record">The datafeed record containing picture source information</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task UpdatePicturesAsync(Product product, DatafeedProductSyncTask.DatafeedCsvFeedModel record)
    {
        if (record.ProductSourceId > 0)
        {
            await UpdateProductSourcePicturesAsync(product, record).ConfigureAwait(false);
            return;
        }

        // Assume that if the product is not a Product Source, that it is then from a price book
        await UpdatePriceBookPicturesAsync(product, record).ConfigureAwait(false);
    }

    /// <summary>
    /// Logs a product synchronisation failure to the activity log
    /// </summary>
    /// <param name="sku">The SKU of the product that failed to sync</param>
    /// <param name="errorMessage">The error message describing the failure</param>
    /// <returns>A task representing the asynchronous operation</returns>
    private async Task LogProductSyncFailureAsync(string sku, string errorMessage)
    {
        await _customerActivityService.InsertActivityAsync(
            "SyncProductFailed",
            $"Syncing product with SKU {sku} failed: {errorMessage}",
            null
        ).ConfigureAwait(false);
    }
}