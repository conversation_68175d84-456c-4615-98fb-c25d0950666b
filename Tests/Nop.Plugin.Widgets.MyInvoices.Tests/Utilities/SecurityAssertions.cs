using Newtonsoft.Json;
using System.Reflection;

namespace Nop.Plugin.Widgets.MyInvoices.Tests.Utilities;

public static class SecurityAssertions
{
    private static readonly string[] SensitivePropertyNames =
    [
        "BuyPrice", "UnitBuyPrice", "BuyPriceSubtotal", "BuyPriceTotal",
        "BuyPriceGST", "BuyPriceInc", "GrossProfit", "MarginPercent",
        "Margin", "Profit", "Cost"
    ];

    public static void AssertNoSensitiveDataExposed<T>(T model) where T : class
    {
        // Check the model type for sensitive properties
        var type = typeof(T);
        AssertTypeHasNoSensitiveProperties(type);

        // Serialize to JSON and check for sensitive strings
        var json = JsonConvert.SerializeObject(model);
        AssertJsonHasNoSensitiveData(json);

        // Recursively check nested types
        CheckNestedTypes(model);
    }

    private static void AssertTypeHasNoSensitiveProperties(Type type)
    {
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        foreach (var property in properties)
        {
            foreach (var sensitiveName in SensitivePropertyNames)
            {
                Assert.That(property.Name.Contains(sensitiveName, StringComparison.OrdinalIgnoreCase), 
                    Is.False, 
                    $"Type {type.Name} contains sensitive property: {property.Name}");
            }
        }
    }

    private static void AssertJsonHasNoSensitiveData(string json)
    {
        foreach (var sensitiveName in SensitivePropertyNames)
        {
            Assert.That(json.Contains(sensitiveName, StringComparison.OrdinalIgnoreCase), 
                Is.False, 
                $"JSON contains sensitive data: {sensitiveName}");
        }
    }

    private static void CheckNestedTypes(object model)
    {
        if (model == null)
        {
            return;
        }

        var type = model.GetType();
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            // Skip primitive types and strings
            if (property.PropertyType.IsPrimitive || property.PropertyType == typeof(string))
            {
                continue;
            }

            // Check if it's a custom type (not in System namespace)
            if (!property.PropertyType.Namespace?.StartsWith("System") ?? false)
            {
                AssertTypeHasNoSensitiveProperties(property.PropertyType);
            }

            // Check collections
            if (property.PropertyType.IsGenericType)
            {
                var genericArgs = property.PropertyType.GetGenericArguments();
                foreach (var genericArg in genericArgs)
                {
                    if (!genericArg.Namespace?.StartsWith("System") ?? false)
                    {
                        AssertTypeHasNoSensitiveProperties(genericArg);
                    }
                }
            }
        }
    }
}