// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace Nop.Plugin.Widgets.MyInvoices.Tests.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("View Invoices")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    public partial class ViewInvoicesFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "View Invoices", "    As a school administrator\r\n    I want to view my school\'s invoices\r\n    So th" +
                "at I can track our financial transactions and payments", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ViewInvoices.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 6
#line hidden
#line 7
    await testRunner.GivenAsync("I am a registered school administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 8
    await testRunner.AndAsync("my school is linked to a CRM account", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("View invoices successfully")]
        public async global::System.Threading.Tasks.Task ViewInvoicesSuccessfully()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View invoices successfully", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 10
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 11
    await testRunner.GivenAsync("I have 5 invoices in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 12
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 13
    await testRunner.ThenAsync("I should see all 5 invoices displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 14
    await testRunner.AndAsync("each invoice should show invoice number, date, amount, and status", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 15
    await testRunner.AndAsync("the page should display my school name", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("View invoices with pagination")]
        public async global::System.Threading.Tasks.Task ViewInvoicesWithPagination()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View invoices with pagination", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 17
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 18
    await testRunner.GivenAsync("I have 25 invoices in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 19
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 20
    await testRunner.ThenAsync("I should see 10 invoices on the first page", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 21
    await testRunner.AndAsync("I should see pagination controls", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 22
    await testRunner.AndAsync("the pagination should show \"Page 1 of 3\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 23
    await testRunner.WhenAsync("I click on page 2", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 24
    await testRunner.ThenAsync("I should see the next 10 invoices", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 25
    await testRunner.AndAsync("the pagination should show \"Page 2 of 3\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("View empty invoice list")]
        public async global::System.Threading.Tasks.Task ViewEmptyInvoiceList()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View empty invoice list", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 27
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 28
    await testRunner.GivenAsync("I have no invoices in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 29
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 30
    await testRunner.ThenAsync("I should see an empty state message", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 31
    await testRunner.AndAsync("the message should say \"No invoices available\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 32
    await testRunner.AndAsync("I should not see pagination controls", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("View invoices with different statuses")]
        public async global::System.Threading.Tasks.Task ViewInvoicesWithDifferentStatuses()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("View invoices with different statuses", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 34
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table1 = new global::Reqnroll.Table(new string[] {
                            "InvoiceNumber",
                            "Amount",
                            "Status",
                            "DaysOld"});
                table1.AddRow(new string[] {
                            "10001",
                            "1500",
                            "Paid",
                            "20"});
                table1.AddRow(new string[] {
                            "10002",
                            "2000",
                            "Outstanding",
                            "15"});
                table1.AddRow(new string[] {
                            "10003",
                            "1800",
                            "Outstanding",
                            "45"});
#line 35
    await testRunner.GivenAsync("I have the following invoices with status information:", ((string)(null)), table1, "Given ");
#line hidden
#line 40
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 41
    await testRunner.ThenAsync("I should see invoice 10001 marked as \"Paid\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 42
    await testRunner.AndAsync("I should see invoice 10002 marked as \"Outstanding\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 43
    await testRunner.AndAsync("I should see invoice 10003 marked as \"Overdue\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Search invoices by reference")]
        public async global::System.Threading.Tasks.Task SearchInvoicesByReference()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Search invoices by reference", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 45
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table2 = new global::Reqnroll.Table(new string[] {
                            "InvoiceNumber",
                            "YourReference",
                            "ShippingReference"});
                table2.AddRow(new string[] {
                            "10001",
                            "PROJECT-A-001",
                            "SHIP-A-001"});
                table2.AddRow(new string[] {
                            "10002",
                            "PROJECT-B-002",
                            "SHIP-B-002"});
                table2.AddRow(new string[] {
                            "10003",
                            "PROJECT-A-003",
                            "SHIP-A-003"});
#line 46
    await testRunner.GivenAsync("I have the following invoices with reference information:", ((string)(null)), table2, "Given ");
#line hidden
#line 51
    await testRunner.WhenAsync("I search for \"PROJECT-A\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 52
    await testRunner.ThenAsync("I should see 2 invoices in the results", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 53
    await testRunner.AndAsync("I should see invoices 10001 and 10003", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 54
    await testRunner.AndAsync("I should not see invoice 10002", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Search invoices by invoice number")]
        public async global::System.Threading.Tasks.Task SearchInvoicesByInvoiceNumber()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Search invoices by invoice number", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 56
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 57
    await testRunner.GivenAsync("I have invoices with numbers 10001, 10002, and 20001", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 58
    await testRunner.WhenAsync("I search for \"20001\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 59
    await testRunner.ThenAsync("I should see 1 invoice in the results", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 60
    await testRunner.AndAsync("I should see invoice 20001", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Search with no results")]
        public async global::System.Threading.Tasks.Task SearchWithNoResults()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Search with no results", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 62
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 63
    await testRunner.GivenAsync("I have 3 invoices in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 64
    await testRunner.WhenAsync("I search for \"NONEXISTENT\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 65
    await testRunner.ThenAsync("I should see no invoices in the results", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 66
    await testRunner.AndAsync("I should see a message \"No invoices found for \\\"NONEXISTENT\\\"\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 67
    await testRunner.AndAsync("I should see a \"View All Invoices\" button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Download invoice PDF")]
        public async global::System.Threading.Tasks.Task DownloadInvoicePDF()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Download invoice PDF", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 69
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 70
    await testRunner.GivenAsync("I have an invoice with number 10001", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 71
    await testRunner.WhenAsync("I click the download PDF button for invoice 10001", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 72
    await testRunner.ThenAsync("a PDF file should be downloaded", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 73
    await testRunner.AndAsync("the filename should be \"Invoice-10001.pdf\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Non-admin user cannot access invoices")]
        public async global::System.Threading.Tasks.Task Non_AdminUserCannotAccessInvoices()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Non-admin user cannot access invoices", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 75
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 76
    await testRunner.GivenAsync("I am logged in as a regular user", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 77
    await testRunner.AndAsync("I am not a school administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 78
    await testRunner.WhenAsync("I try to navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 79
    await testRunner.ThenAsync("I should see an error message", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 80
    await testRunner.AndAsync("I should be redirected away from the invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("School without CRM link cannot access invoices")]
        public async global::System.Threading.Tasks.Task SchoolWithoutCRMLinkCannotAccessInvoices()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("School without CRM link cannot access invoices", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 82
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 83
    await testRunner.GivenAsync("I am a registered school administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 84
    await testRunner.ButAsync("my school is not linked to a CRM account", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 85
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 86
    await testRunner.ThenAsync("I should see an error message about CRM linkage", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 87
    await testRunner.AndAsync("I should not see any invoices", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("API service unavailable")]
        public async global::System.Threading.Tasks.Task APIServiceUnavailable()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("API service unavailable", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 89
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 90
    await testRunner.GivenAsync("I am a valid school administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 91
    await testRunner.ButAsync("the invoice API service is unavailable", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 92
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 93
    await testRunner.ThenAsync("I should see an error message about service availability", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 94
    await testRunner.AndAsync("I should see a retry button", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Large invoice amounts display correctly")]
        public async global::System.Threading.Tasks.Task LargeInvoiceAmountsDisplayCorrectly()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Large invoice amounts display correctly", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 96
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 97
    await testRunner.GivenAsync("I have an invoice with amount $1,234,567.89", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 98
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 99
    await testRunner.ThenAsync("I should see the amount displayed as \"$1,234,567.89\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 100
    await testRunner.AndAsync("the formatting should be correct", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Invoice count display")]
        public async global::System.Threading.Tasks.Task InvoiceCountDisplay()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Invoice count display", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 102
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 103
    await testRunner.GivenAsync("I have 15 invoices in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 104
    await testRunner.WhenAsync("I navigate to the my invoices page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 105
    await testRunner.ThenAsync("I should see \"15 Invoices\" in the header", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 106
    await testRunner.WhenAsync("I have 1 invoice in my account", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 107
    await testRunner.AndAsync("I refresh the page", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 108
    await testRunner.ThenAsync("I should see \"1 Invoice\" in the header", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion
