@UnlinkCrmContact
Feature: API endpoint to unlink a Nop customer from a CRM contact

    Scenario: Malicious attacker tries to access the endpoint directly but doesn't have the API key
        Given a malicious attacker who doesn't have the API key
        When they try to access the endpoint directly
        Then the status code should be 401 Unauthorised
        And the error message 'Missing API key' should be returned

    Scenario: Malicious attacker tries to access the endpoint directly using random API keys
        Given a malicious attacker who is using random API keys:
          | ApiKey     |
          | api-key-01 |
          | api-key-02 |
        When they try to access the endpoint directly
        Then the status code should be 401 Unauthorised
        And the error message 'Invalid API key' should be returned

    Scenario: Malicious attacker tries to access the endpoint directly with an invalid school index
        Given a malicious attacker who uses the following arguments:
          | crmContactId | nopCustomerId | schoolIdx |
          | 1            | 1             | 5         |
        When they try to access the endpoint directly
        Then the status code should be 400 Bad Request
        And the error message 'Invalid school index' should be returned

    Scenario: Malicious attacker tries to access the endpoint directly with an invalid CRM contact ID
        Given a malicious attacker who uses the following arguments:
          | crmContactId | nopCustomerId | schoolIdx |
          | 5            | 1             | 0         |
        And the Nop customer is not linked to the CRM contact
        When they try to access the endpoint directly
        Then the status code should be 400 Bad Request
        And the error message 'Invalid CRM contact ID' should be returned

    Scenario Outline: CRM calls the endpoint with valid arguments
        Given a user in CRM opens the 'Manage Nop Customer' modal for CRM contact <crmContactId> from Nop customer <nopCustomerId> with school index <schoolIdx>
        When they click the 'Unmap' button
        Then the Nop customers' <schoolIdx> school's CrmContactId should be set to null
        And the response should contain a 'Success' property that is set to true

        Examples:
          | crmContactId | nopCustomerId | schoolIdx |
          | 2            | 2             | 0         |