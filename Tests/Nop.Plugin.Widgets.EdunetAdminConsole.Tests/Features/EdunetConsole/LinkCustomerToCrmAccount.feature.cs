// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace Nop.Plugin.Widgets.EdunetAdminConsole.Tests.Features.EdunetConsole
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability to link a Nop customer to a CRM account in the Edunet console")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("LinkCustomerToCrmAccount")]
    public partial class AbilityToLinkANopCustomerToACRMAccountInTheEdunetConsoleFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "LinkCustomerToCrmAccount"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/EdunetConsole", "Ability to link a Nop customer to a CRM account in the Edunet console", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "LinkCustomerToCrmAccount.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Malicious attacker tries to call the endpoint directly")]
        [NUnit.Framework.TestCaseAttribute("not in the registered role", "login", null)]
        [NUnit.Framework.TestCaseAttribute("not an Edunet admin", "login", null)]
        public async global::System.Threading.Tasks.Task MaliciousAttackerTriesToCallTheEndpointDirectly(string role, string redirectedPage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("role", role);
            argumentsOfScenario.Add("redirectedPage", redirectedPage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Malicious attacker tries to call the endpoint directly", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync(string.Format("a malicious attacker who is \'{0}\'", role), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("they try to access the endpoint directly", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync(string.Format("they should be redirect to the page \'{0}\'", redirectedPage), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Edunet admin tries to link a new customer to a CRM account when the customer hasn" +
            "\'t yet been approved")]
        public async global::System.Threading.Tasks.Task EdunetAdminTriesToLinkANewCustomerToACRMAccountWhenTheCustomerHasntYetBeenApproved()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Edunet admin tries to link a new customer to a CRM account when the customer hasn" +
                    "\'t yet been approved", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 14
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 15
        await testRunner.GivenAsync("a new user (\'<EMAIL>\') has signed up", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 16
        await testRunner.AndAsync("they haven\'t been approved", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 17
        await testRunner.AndAsync("an Edunet admin has the Link modal open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 18
        await testRunner.WhenAsync("they click the Link button to link the user to Dromana Secondary College", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 19
        await testRunner.ThenAsync("the page should be refreshed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 20
        await testRunner.AndAsync("an error notification with the message \"<EMAIL> hasn\'t been approved.\"" +
                        " should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User has signed up for Dromana Secondary College")]
        public async global::System.Threading.Tasks.Task UserHasSignedUpForDromanaSecondaryCollege()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User has signed up for Dromana Secondary College", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 22
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 23
        await testRunner.GivenAsync("a new user (\'<EMAIL>\') has signed up for Dromana Secondary College", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 24
        await testRunner.AndAsync("an Edunet admin has approved them", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 25
        await testRunner.AndAsync("an Edunet admin has the Link modal open", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 26
        await testRunner.WhenAsync("they click the Link button to link the user to Dromana Secondary College", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 27
        await testRunner.ThenAsync("the new users\' customer attribute XML should be updated to contain a new value fo" +
                        "r the mapped school with LinkedTo field set", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 28
        await testRunner.AndAsync("the page should be refreshed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 29
        await testRunner.AndAsync("a success notification with the message \'<EMAIL> has successfully been" +
                        " linked to Dromana Secondary College.\' should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion
