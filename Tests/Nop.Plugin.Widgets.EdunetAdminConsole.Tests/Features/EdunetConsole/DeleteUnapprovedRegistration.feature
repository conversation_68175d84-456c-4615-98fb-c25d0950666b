@DeleteUnapprovedRegistration
Feature: Ability to delete an unapproved customer registration

    Scenario Template: A malicious attacker tries to access the Delete Customer Registration endpoint directly via URL
        Given a malicious attacker who is '<role>' and signed in with school index <schoolIndex>
        When they try to access the Delete Customer Registration endpoint directly
        Then they should be redirected to the page '<redirectedPage>'

        Examples:
          | role                       | redirectedPage | schoolIndex |
          | not in the registered role | login          | 0           |
          | not an Edunet admin        | login          | 0           |

    Scenario Template: <userEmail> who is a parent has requested to sign up for the e-store
        Given an Edunet admin is on the Edunet Console page
        And a parent with email '<userEmail>' has requested to sign up for the e-store
        When the Edunet admin clicks the 'Delete' button
        Then the customer registration should be deleted
        And the page should be refreshed
        And a success notification with the message '<successMessage>' should be displayed

        Examples:
          | userEmail         | successMessage                                     |
          | <EMAIL> | The customer registration was successfully deleted |