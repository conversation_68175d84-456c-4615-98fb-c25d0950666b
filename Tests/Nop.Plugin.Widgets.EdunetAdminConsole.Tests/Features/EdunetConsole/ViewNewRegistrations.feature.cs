// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace Nop.Plugin.Widgets.EdunetAdminConsole.Tests.Features.EdunetConsole
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability to view new customer registrations needing review/approval in the Edunet " +
        "console")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("ViewNewRegistrations")]
    public partial class AbilityToViewNewCustomerRegistrationsNeedingReviewApprovalInTheEdunetConsoleFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "ViewNewRegistrations"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features/EdunetConsole", "Ability to view new customer registrations needing review/approval in the Edunet " +
                "console", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ViewNewRegistrations.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Malicious attacker tries to call the endpoint directly")]
        [NUnit.Framework.TestCaseAttribute("not in the registered role", "login", null)]
        [NUnit.Framework.TestCaseAttribute("not an Edunet admin", "login", null)]
        public async global::System.Threading.Tasks.Task MaliciousAttackerTriesToCallTheEndpointDirectly(string role, string redirectedPage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("role", role);
            argumentsOfScenario.Add("redirectedPage", redirectedPage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Malicious attacker tries to call the endpoint directly", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync(string.Format("a malicious attacker who is \'{0}\'", role), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("they try to access the endpoint directly", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync(string.Format("they should be redirected to the page \'{0}\'", redirectedPage), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A new user has signed up")]
        public async global::System.Threading.Tasks.Task ANewUserHasSignedUp()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A new user has signed up", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 14
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 15
        await testRunner.GivenAsync("a new user (\'<EMAIL>\') has signed up", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 16
        await testRunner.WhenAsync("an Edunet admin visits the Edunet Console page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 17
        await testRunner.ThenAsync("they should see a list of new registrations with \'<EMAIL>\' contained w" +
                        "ithin them", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A new user has signed up and been approved")]
        public async global::System.Threading.Tasks.Task ANewUserHasSignedUpAndBeenApproved()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A new user has signed up and been approved", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 19
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 20
        await testRunner.GivenAsync("a new user (\'<EMAIL>\') has signed up", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 21
        await testRunner.AndAsync("an Edunet admin has approved them", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 22
        await testRunner.WhenAsync("an Edunet admin visits the Edunet Console page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 23
        await testRunner.ThenAsync("they should see a list of new registrations with \'<EMAIL>\' contained w" +
                        "ithin them", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A new user has signed up, been approved and had all schools linked to a CRM accou" +
            "nt")]
        public async global::System.Threading.Tasks.Task ANewUserHasSignedUpBeenApprovedAndHadAllSchoolsLinkedToACRMAccount()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A new user has signed up, been approved and had all schools linked to a CRM accou" +
                    "nt", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 25
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 26
        await testRunner.GivenAsync("a new user (\'<EMAIL>\') has signed up", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 27
        await testRunner.AndAsync("an Edunet admin has approved them", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 28
        await testRunner.AndAsync("an Edunet admin has linked all their schools to a corresponding account in CRM", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 29
        await testRunner.WhenAsync("an Edunet admin visits the Edunet Console page", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 30
        await testRunner.ThenAsync("they should see a list of new registrations that DON\'T contain \'<EMAIL>.a" +
                        "u\'", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion
