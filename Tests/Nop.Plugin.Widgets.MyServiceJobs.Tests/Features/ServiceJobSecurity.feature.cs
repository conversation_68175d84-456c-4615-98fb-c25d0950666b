// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace Nop.Plugin.Widgets.MyServiceJobs.Tests.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Service Job Security")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    public partial class ServiceJobSecurityFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = ((string[])(null));
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Service Job Security", "    As a system administrator\r\n    I want to ensure that service job access is pr" +
                "operly secured\r\n    So that only authorized school administrators can view their" +
                " school\'s service jobs", global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "ServiceJobSecurity.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        public virtual async global::System.Threading.Tasks.Task FeatureBackgroundAsync()
        {
#line 6
#line hidden
#line 7
    await testRunner.GivenAsync("the service job system is available", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Valid school administrator can access service jobs")]
        public async global::System.Threading.Tasks.Task ValidSchoolAdministratorCanAccessServiceJobs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Valid school administrator can access service jobs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 9
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 10
    await testRunner.GivenAsync("I am a registered customer with the \"Registered\" role", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 11
    await testRunner.AndAsync("I am an administrator for \"Test Primary School\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 12
    await testRunner.AndAsync("my school is linked to CRM account \"123456\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 13
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 14
    await testRunner.ThenAsync("my access should be granted", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 15
    await testRunner.AndAsync("I should be able to view service jobs for CRM account \"123456\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Guest user cannot access service jobs")]
        public async global::System.Threading.Tasks.Task GuestUserCannotAccessServiceJobs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Guest user cannot access service jobs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 17
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 18
    await testRunner.GivenAsync("I am a guest user", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 19
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 20
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 21
    await testRunner.AndAsync("I should receive an error \"Customer not authenticated\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("User without registered role cannot access service jobs")]
        public async global::System.Threading.Tasks.Task UserWithoutRegisteredRoleCannotAccessServiceJobs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("User without registered role cannot access service jobs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 23
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 24
    await testRunner.GivenAsync("I am a customer without the \"Registered\" role", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 25
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 26
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 27
    await testRunner.AndAsync("I should receive an error about required permissions", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Non-administrator cannot access service jobs")]
        public async global::System.Threading.Tasks.Task Non_AdministratorCannotAccessServiceJobs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Non-administrator cannot access service jobs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 29
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 30
    await testRunner.GivenAsync("I am a registered customer", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 31
    await testRunner.ButAsync("I am not an administrator for any school", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 32
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 33
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 34
    await testRunner.AndAsync("I should receive an error \"Customer is not an administrator for this school\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("School without CRM linkage cannot access service jobs")]
        public async global::System.Threading.Tasks.Task SchoolWithoutCRMLinkageCannotAccessServiceJobs()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("School without CRM linkage cannot access service jobs", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 36
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 37
    await testRunner.GivenAsync("I am a registered customer with the \"Registered\" role", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 38
    await testRunner.AndAsync("I am an administrator for \"Test School\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 39
    await testRunner.ButAsync("my school is not linked to any CRM account", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 40
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 41
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 42
    await testRunner.AndAsync("I should receive an error \"School is not linked to a CRM account\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Cross-school access prevention")]
        public async global::System.Threading.Tasks.Task Cross_SchoolAccessPrevention()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Cross-school access prevention", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 44
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 45
    await testRunner.GivenAsync("I am an administrator for \"School A\" linked to CRM account \"111111\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 46
    await testRunner.AndAsync("there exists \"School B\" linked to CRM account \"222222\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 47
    await testRunner.WhenAsync("I request access to service jobs for CRM account \"222222\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 48
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 49
    await testRunner.AndAsync("I should not be able to view service jobs for \"School B\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Multiple schools - correct school selection")]
        public async global::System.Threading.Tasks.Task MultipleSchools_CorrectSchoolSelection()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Multiple schools - correct school selection", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 51
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
                global::Reqnroll.Table table1 = new global::Reqnroll.Table(new string[] {
                            "SchoolName",
                            "IsAdmin",
                            "CrmAccount"});
                table1.AddRow(new string[] {
                            "Primary A",
                            "true",
                            "111111"});
                table1.AddRow(new string[] {
                            "Secondary B",
                            "false",
                            "222222"});
                table1.AddRow(new string[] {
                            "Primary C",
                            "true",
                            "333333"});
#line 52
    await testRunner.GivenAsync("I am associated with multiple schools:", ((string)(null)), table1, "Given ");
#line hidden
#line 57
    await testRunner.AndAsync("I have selected \"Primary A\" as my current school", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 58
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 59
    await testRunner.ThenAsync("my access should be granted for \"Primary A\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 60
    await testRunner.AndAsync("I should be able to view service jobs for CRM account \"111111\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("School selection via cookie")]
        public async global::System.Threading.Tasks.Task SchoolSelectionViaCookie()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("School selection via cookie", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 62
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 63
    await testRunner.GivenAsync("I am an administrator for multiple schools", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 64
    await testRunner.AndAsync("I have a school selection cookie set to index \"1\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
                global::Reqnroll.Table table2 = new global::Reqnroll.Table(new string[] {
                            "Index",
                            "SchoolName",
                            "IsAdmin",
                            "CrmAccount"});
                table2.AddRow(new string[] {
                            "0",
                            "School A",
                            "false",
                            "111111"});
                table2.AddRow(new string[] {
                            "1",
                            "School B",
                            "true",
                            "222222"});
#line 65
    await testRunner.AndAsync("my schools are:", ((string)(null)), table2, "And ");
#line hidden
#line 69
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 70
    await testRunner.ThenAsync("\"School B\" should be selected", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 71
    await testRunner.AndAsync("I should be able to view service jobs for CRM account \"222222\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("No school cookie defaults to first admin school")]
        public async global::System.Threading.Tasks.Task NoSchoolCookieDefaultsToFirstAdminSchool()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("No school cookie defaults to first admin school", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 73
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 74
    await testRunner.GivenAsync("I am an administrator for multiple schools", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 75
    await testRunner.AndAsync("I have no school selection cookie", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
                global::Reqnroll.Table table3 = new global::Reqnroll.Table(new string[] {
                            "SchoolName",
                            "IsAdmin",
                            "CrmAccount"});
                table3.AddRow(new string[] {
                            "School A",
                            "false",
                            "111111"});
                table3.AddRow(new string[] {
                            "School B",
                            "true",
                            "222222"});
                table3.AddRow(new string[] {
                            "School C",
                            "true",
                            "333333"});
#line 76
    await testRunner.AndAsync("my schools are:", ((string)(null)), table3, "And ");
#line hidden
#line 81
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 82
    await testRunner.ThenAsync("\"School B\" should be selected as the first admin school", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 83
    await testRunner.AndAsync("I should be able to view service jobs for CRM account \"222222\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("No admin schools available")]
        public async global::System.Threading.Tasks.Task NoAdminSchoolsAvailable()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("No admin schools available", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 85
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 86
    await testRunner.GivenAsync("I am associated with schools but not an administrator for any", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 87
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 88
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 89
    await testRunner.AndAsync("I should receive an error about no admin school available", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Invalid school cookie index")]
        public async global::System.Threading.Tasks.Task InvalidSchoolCookieIndex()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Invalid school cookie index", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 91
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 92
    await testRunner.GivenAsync("I am an administrator for 2 schools", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 93
    await testRunner.AndAsync("I have a school selection cookie set to index \"5\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 94
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 95
    await testRunner.ThenAsync("the system should fall back to the first admin school", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 96
    await testRunner.AndAsync("my access should be granted", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Malformed customer attributes")]
        public async global::System.Threading.Tasks.Task MalformedCustomerAttributes()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Malformed customer attributes", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 98
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 99
    await testRunner.GivenAsync("I am a registered customer", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 100
    await testRunner.ButAsync("my customer attributes are malformed", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 101
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 102
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 103
    await testRunner.AndAsync("I should receive an error about retrieving customer school", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Missing schools attribute")]
        public async global::System.Threading.Tasks.Task MissingSchoolsAttribute()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Missing schools attribute", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 105
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 106
    await testRunner.GivenAsync("I am a registered customer", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 107
    await testRunner.ButAsync("I have no \"Schools\" customer attribute", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 108
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 109
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 110
    await testRunner.AndAsync("I should receive an error \"Schools attribute not found\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("CRM account ID validation")]
        public async global::System.Threading.Tasks.Task CRMAccountIDValidation()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("CRM account ID validation", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 112
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 113
    await testRunner.GivenAsync("I am a valid school administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 114
    await testRunner.AndAsync("my school is linked to CRM account \"123456\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 115
    await testRunner.WhenAsync("I request access to service jobs for CRM account \"123456\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 116
    await testRunner.ThenAsync("my access should be granted", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 117
    await testRunner.WhenAsync("I request access to service jobs for CRM account \"999999\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 118
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Null customer handling")]
        public async global::System.Threading.Tasks.Task NullCustomerHandling()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Null customer handling", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 120
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 121
    await testRunner.GivenAsync("no customer is provided", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 122
    await testRunner.WhenAsync("access validation is performed", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 123
    await testRunner.ThenAsync("access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 124
    await testRunner.AndAsync("an appropriate error message should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("Exception handling during validation")]
        public async global::System.Threading.Tasks.Task ExceptionHandlingDuringValidation()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("Exception handling during validation", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 126
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 127
    await testRunner.GivenAsync("I am a registered customer", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 128
    await testRunner.ButAsync("the customer service throws an exception", ((string)(null)), ((global::Reqnroll.Table)(null)), "But ");
#line hidden
#line 129
    await testRunner.WhenAsync("I request access to service jobs", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 130
    await testRunner.ThenAsync("my access should be denied", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 131
    await testRunner.AndAsync("the exception should be handled gracefully", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 132
    await testRunner.AndAsync("an error message should be returned", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("School admin permission check")]
        public async global::System.Threading.Tasks.Task SchoolAdminPermissionCheck()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("School admin permission check", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 134
this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 6
await this.FeatureBackgroundAsync();
#line hidden
#line 135
    await testRunner.GivenAsync("I have a school \"Test School\"", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 136
    await testRunner.AndAsync("I am marked as an administrator for this school", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 137
    await testRunner.WhenAsync("my admin status is checked", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 138
    await testRunner.ThenAsync("I should be confirmed as an administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 139
    await testRunner.GivenAsync("I am not marked as an administrator for this school", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 140
    await testRunner.WhenAsync("my admin status is checked", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 141
    await testRunner.ThenAsync("I should not be confirmed as an administrator", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion
