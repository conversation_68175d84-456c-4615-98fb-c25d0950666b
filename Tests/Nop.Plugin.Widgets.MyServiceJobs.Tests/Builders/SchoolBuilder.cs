using Nop.Plugin.ExternalAuth.MultiSSO.Models;

namespace Nop.Plugin.Widgets.MyServiceJobs.Tests.Builders;

public class SchoolBuilder
{
    private readonly School _school;

    public SchoolBuilder()
    {
        _school = new School
        {
            Name = "Test School",
            Address = "123 Test Street",
            PhoneNumber = "1234567890",
            IsAdmin = false,
            LinkedTo = null
        };
    }

    public SchoolBuilder WithName(string name)
    {
        _school.Name = name;
        return this;
    }

    public SchoolBuilder AsAdmin(bool isAdmin = true)
    {
        _school.IsAdmin = isAdmin;
        return this;
    }

    public SchoolBuilder WithCrmLink(string crmLink)
    {
        _school.LinkedTo = crmLink;
        return this;
    }

    public SchoolBuilder WithoutCrmLink()
    {
        _school.LinkedTo = null;
        return this;
    }

    public School Build()
    {
        return _school;
    }

    /// <summary>
    /// Creates a standard admin school with CRM linkage for common test scenarios
    /// </summary>
    public static School CreateAdminSchool(string name = "Test Primary School", string crmAccountId = "123456")
    {
        return new SchoolBuilder()
            .WithName(name)
            .AsAdmin(true)
            .WithCrmLink($"CRM:{crmAccountId}")
            .Build();
    }

    /// <summary>
    /// Creates a non-admin school for permission testing
    /// </summary>
    public static School CreateNonAdminSchool(string name = "Test Secondary School", string crmAccountId = "789012")
    {
        return new SchoolBuilder()
            .WithName(name)
            .AsAdmin(false)
            .WithCrmLink($"CRM:{crmAccountId}")
            .Build();
    }

    /// <summary>
    /// Creates a school without CRM linkage for testing business rules
    /// </summary>
    public static School CreateUnlinkedSchool(string name = "Unlinked School")
    {
        return new SchoolBuilder()
            .WithName(name)
            .AsAdmin(true)
            .WithoutCrmLink()
            .Build();
    }
}
