using Microsoft.AspNetCore.Http;
using Moq;
using Newtonsoft.Json;
using Nop.Core.Domain.Customers;
using Nop.Services.Common;
using Nop.Services.Customers;

namespace Nop.Plugin.Widgets.MyServiceJobs.Tests.Services;

/// <summary>
/// Comprehensive unit tests for ServiceJobSecurityService.
/// 
/// This service handles all security and authorization logic for service job access:
/// 1. Customer authentication validation
/// 2. Role-based authorization (Registered role required)
/// 3. School administrator permission verification
/// 4. CRM account linkage validation
/// 5. School selection from customer attributes and cookies
/// 
/// Security Coverage:
/// - Authentication requirements
/// - Authorization boundaries
/// - School admin permissions
/// - CRM linkage business rules
/// - Cross-school access prevention
/// </summary>
[TestFixture]
public class ServiceJobSecurityServiceTests : TestBase
{
    private ServiceJobSecurityService _securityService;

    [SetUp]
    public override void Setup()
    {
        base.Setup();
        
        _securityService = new ServiceJobSecurityService(
            MockCustomerService.Object,
            MockCustomerAttributeService.Object,
            MockCustomerAttributeParser.Object,
            MockHttpContextAccessor.Object
        );
    }

    #region ValidateCustomerAccessAsync Tests

    [Test]
    public async Task ValidateCustomerAccessAsync_ValidSchoolAdmin_ReturnsSuccess()
    {
        // Arrange
        var scenario = TestDataFactory.CreateValidSchoolAdminScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(scenario.Customer);

        // Assert
        SecurityAssertions.AssertSecuritySuccess(result, "valid school admin");
        SecurityAssertions.AssertSchoolAdminAccess(result.Value, "validated customer");
        Assert.Multiple(() =>
        {
            Assert.That(result.Value.Name, Is.EqualTo(scenario.School.Name));
            Assert.That(result.Value.LinkedTo, Is.EqualTo(scenario.School.LinkedTo));
        });
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_NullCustomer_ReturnsFailure()
    {
        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(null);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "not authenticated", "null customer");
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_GuestCustomer_ReturnsFailure()
    {
        // Arrange
        var scenario = TestDataFactory.CreateGuestCustomerScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(scenario.Customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "not authenticated", "guest customer");
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_CustomerWithoutRegisteredRole_ReturnsFailure()
    {
        // Arrange
        var customer = new CustomerBuilder()
            .WithId(1)
            .WithEmail("<EMAIL>")
            .Build(); // No roles added

        MockCustomerService
            .Setup(x => x.IsGuestAsync(customer, It.IsAny<bool>()))
            .ReturnsAsync(false);

        MockCustomerService
            .Setup(x => x.GetCustomerRolesAsync(customer, It.IsAny<bool>()))
            .ReturnsAsync(new List<CustomerRole>());

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "required permissions", "customer without registered role");
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_NonAdminCustomer_ReturnsFailure()
    {
        // Arrange
        var scenario = TestDataFactory.CreateNonAdminCustomerScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(scenario.Customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "not an administrator", "non-admin customer");
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_SchoolWithoutCrmLink_ReturnsFailure()
    {
        // Arrange
        var scenario = TestDataFactory.CreateSchoolWithoutCrmLinkScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(scenario.Customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "not linked to a CRM account", "school without CRM link");
    }

    [Test]
    public async Task ValidateCustomerAccessAsync_CustomerWithMultipleSchools_ReturnsSelectedAdminSchool()
    {
        // Arrange
        var scenario = TestDataFactory.CreateMultipleSchoolsScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.ValidateCustomerAccessAsync(scenario.Customer);

        // Assert
        SecurityAssertions.AssertSecuritySuccess(result, "customer with multiple schools");
        Assert.Multiple(() =>
        {
            Assert.That(result.Value.IsAdmin, Is.True);
            Assert.That(result.Value.Name, Is.EqualTo("Admin School"));
        });
    }

    #endregion

    #region CanAccessServiceJobAsync Tests

    [Test]
    public async Task CanAccessServiceJobAsync_ValidCustomerAndMatchingCrmAccount_ReturnsTrue()
    {
        // Arrange
        var scenario = TestDataFactory.CreateValidSchoolAdminScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.CanAccessServiceJobAsync(scenario.Customer, scenario.CrmAccountId);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task CanAccessServiceJobAsync_ValidCustomerButDifferentCrmAccount_ReturnsFalse()
    {
        // Arrange
        var scenario = TestDataFactory.CreateValidSchoolAdminScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);
        const string differentCrmAccountId = "999999";

        // Act
        var result = await _securityService.CanAccessServiceJobAsync(scenario.Customer, differentCrmAccountId);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CanAccessServiceJobAsync_InvalidCustomer_ReturnsFalse()
    {
        // Arrange
        var scenario = TestDataFactory.CreateGuestCustomerScenario();
        TestDataFactory.SetupMocksForScenario(scenario, this);

        // Act
        var result = await _securityService.CanAccessServiceJobAsync(scenario.Customer, "123456");

        // Assert
        Assert.That(result, Is.False);
    }

    #endregion

    #region IsSchoolAdminAsync Tests

    [Test]
    public async Task IsSchoolAdminAsync_ValidSchoolWithAdminPermission_ReturnsTrue()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();
        var school = SchoolBuilder.CreateAdminSchool();

        // Act
        var result = await _securityService.IsSchoolAdminAsync(customer, school);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public async Task IsSchoolAdminAsync_ValidSchoolWithoutAdminPermission_ReturnsFalse()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();
        var school = SchoolBuilder.CreateNonAdminSchool();

        // Act
        var result = await _securityService.IsSchoolAdminAsync(customer, school);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task IsSchoolAdminAsync_NullCustomer_ReturnsFalse()
    {
        // Arrange
        var school = SchoolBuilder.CreateAdminSchool();

        // Act
        var result = await _securityService.IsSchoolAdminAsync(null, school);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task IsSchoolAdminAsync_NullSchool_ReturnsFalse()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();

        // Act
        var result = await _securityService.IsSchoolAdminAsync(customer, null);

        // Assert
        Assert.That(result, Is.False);
    }

    #endregion

    #region GetCustomerSelectedSchoolAsync Tests

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_ValidCustomerWithSchoolCookie_ReturnsSelectedSchool()
    {
        // Arrange
        var school = SchoolBuilder.CreateAdminSchool();
        var customer = new CustomerBuilder()
            .WithId(1)
            .WithSchools(school)
            .Build();

        var schoolsAttribute = new CustomerAttribute { Id = 1, Name = "Schools" };
        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ReturnsAsync(new List<CustomerAttribute> { schoolsAttribute });

        var schoolJson = JsonConvert.SerializeObject(school);
        MockCustomerAttributeParser
            .Setup(x => x.ParseValues(customer.CustomCustomerAttributesXML, schoolsAttribute.Id))
            .Returns(new List<string> { schoolJson });

        // Setup HTTP context with school cookie
        var httpContext = new Mock<HttpContext>();
        var request = new Mock<HttpRequest>();
        var cookies = new Mock<IRequestCookieCollection>();

        cookies.Setup(x => x.TryGetValue("School", out It.Ref<string>.IsAny))
            .Returns((string key, out string value) =>
            {
                value = "0"; // First school
                return true;
            });

        request.Setup(x => x.Cookies).Returns(cookies.Object);
        httpContext.Setup(x => x.Request).Returns(request.Object);
        MockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext.Object);

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        SecurityAssertions.AssertSecuritySuccess(result, "customer with school cookie");
        Assert.Multiple(() =>
        {
            Assert.That(result.Value.Name, Is.EqualTo(school.Name));
            Assert.That(result.Value.IsAdmin, Is.EqualTo(school.IsAdmin));
        });
    }

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_CustomerWithoutSchoolsAttribute_ReturnsFailure()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();

        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ReturnsAsync(new List<CustomerAttribute>());

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "Schools attribute not found", "customer without schools attribute");
    }

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_CustomerWithoutSchools_ReturnsFailure()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();
        var schoolsAttribute = new CustomerAttribute { Id = 1, Name = "Schools" };

        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ReturnsAsync(new List<CustomerAttribute> { schoolsAttribute });

        MockCustomerAttributeParser
            .Setup(x => x.ParseValues(customer.CustomCustomerAttributesXML, schoolsAttribute.Id))
            .Returns(new List<string>());

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "No schools found", "customer without schools");
    }

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_NoSchoolCookieButHasAdminSchool_ReturnsFirstAdminSchool()
    {
        // Arrange
        var nonAdminSchool = SchoolBuilder.CreateNonAdminSchool("Non-Admin School");
        var adminSchool = SchoolBuilder.CreateAdminSchool("Admin School");
        var customer = new CustomerBuilder()
            .WithId(1)
            .WithSchools(nonAdminSchool, adminSchool)
            .Build();

        var schoolsAttribute = new CustomerAttribute { Id = 1, Name = "Schools" };
        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ReturnsAsync(new List<CustomerAttribute> { schoolsAttribute });

        var schoolsJson = new List<string>
        {
            JsonConvert.SerializeObject(nonAdminSchool),
            JsonConvert.SerializeObject(adminSchool)
        };
        MockCustomerAttributeParser
            .Setup(x => x.ParseValues(customer.CustomCustomerAttributesXML, schoolsAttribute.Id))
            .Returns(schoolsJson);

        SetupDefaultHttpContext(); // No school cookie

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        SecurityAssertions.AssertSecuritySuccess(result, "customer with admin school but no cookie");
        Assert.Multiple(() =>
        {
            Assert.That(result.Value.Name, Is.EqualTo("Admin School"));
            Assert.That(result.Value.IsAdmin, Is.True);
        });
    }

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_NoSchoolCookieAndNoAdminSchool_ReturnsFailure()
    {
        // Arrange
        var nonAdminSchool = SchoolBuilder.CreateNonAdminSchool();
        var customer = new CustomerBuilder()
            .WithId(1)
            .WithSchools(nonAdminSchool)
            .Build();

        var schoolsAttribute = new CustomerAttribute { Id = 1, Name = "Schools" };
        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ReturnsAsync(new List<CustomerAttribute> { schoolsAttribute });

        var schoolJson = JsonConvert.SerializeObject(nonAdminSchool);
        MockCustomerAttributeParser
            .Setup(x => x.ParseValues(customer.CustomCustomerAttributesXML, schoolsAttribute.Id))
            .Returns(new List<string> { schoolJson });

        SetupDefaultHttpContext(); // No school cookie

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        // The method should return the non-admin school (not fail)
        Assert.That(result.IsSuccess, Is.True);
        Assert.That(result.Value, Is.EqualTo(nonAdminSchool));
        Assert.That(result.Value.IsAdmin, Is.False);
    }

    [Test]
    public async Task GetCustomerSelectedSchoolAsync_ExceptionDuringProcessing_ReturnsFailure()
    {
        // Arrange
        var customer = CustomerBuilder.CreateSchoolAdmin();

        MockCustomerAttributeService
            .Setup(x => x.GetAllCustomerAttributesAsync())
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _securityService.GetCustomerSelectedSchoolAsync(customer);

        // Assert
        SecurityAssertions.AssertSecurityFailure(result, "Error retrieving customer school", "exception during processing");
    }

    #endregion
}
