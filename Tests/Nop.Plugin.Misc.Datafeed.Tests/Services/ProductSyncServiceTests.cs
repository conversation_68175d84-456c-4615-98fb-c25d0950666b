using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CSharpFunctionalExtensions;
using FluentAssertions;
using Moq;
using Nop.Core;
using Nop.Core.Domain.Catalog;
using Nop.Core.Domain.Common;
using Nop.Core.Domain.Customers;
using Nop.Core.Domain.Discounts;
using Nop.Core.Domain.Logging;
using Nop.Core.Domain.Media;
using Nop.Core.Domain.Orders;
using Nop.Core.Domain.Seo;
using Nop.Core.Domain.Shipping;
using Nop.Core.Domain.Stores;
using Nop.Core.Domain.Tax;
using Nop.Plugin.Misc.Datafeed.Services;
using Nop.Plugin.Misc.EdunetCore.Services;
using Nop.Services.Catalog;
using Nop.Services.Localization;
using Nop.Services.Logging;
using Nop.Services.Media;
using Nop.Services.Seo;
using Nop.Services.Tax;
using Nop.Tests;
using NUnit.Framework;
using static Nop.Plugin.Misc.Datafeed.Services.DatafeedProductSyncTask;

namespace Nop.Plugin.Misc.Datafeed.Tests.Services;

[TestFixture]
public class ProductSyncServiceTests : BaseNopTest
{
    private ProductSyncService _sut;
    private Mock<IProductService> _productServiceMock;
    private Mock<IPictureService> _pictureServiceMock;
    private Mock<ICategoryService> _categoryServiceMock;
    private Mock<ILocalizationService> _localizationServiceMock;
    private Mock<ICustomerActivityService> _customerActivityServiceMock;
    private Mock<IUrlRecordService> _urlRecordServiceMock;
    private Mock<IProductAttributeService> _productAttributeServiceMock;
    private Mock<ISpecificationAttributeService> _specificationAttributeServiceMock;
    private Mock<ITaxCategoryService> _taxCategoryServiceMock;
    private Mock<IManufacturerService> _manufacturerServiceMock;
    private Mock<ISharedDatafeedApiClient> _sharedDatafeedApiClientMock;

    private readonly Dictionary<string, Product> _testProductsBySku = new();
    private readonly Dictionary<int, Product> _testProductsById = new();
    private int _nextProductId = 1;


    [SetUp]
    public void SetUp()
    {
        _testProductsBySku.Clear();
        _testProductsById.Clear();
        _nextProductId = 1;

        _productServiceMock = new Mock<IProductService>();

        _pictureServiceMock = new Mock<IPictureService>();
        _categoryServiceMock = new Mock<ICategoryService>();
        _localizationServiceMock = new Mock<ILocalizationService>();
        _customerActivityServiceMock = new Mock<ICustomerActivityService>();
        _urlRecordServiceMock = new Mock<IUrlRecordService>();
        _productAttributeServiceMock = new Mock<IProductAttributeService>();
        _specificationAttributeServiceMock = new Mock<ISpecificationAttributeService>();
        _taxCategoryServiceMock = new Mock<ITaxCategoryService>();
        _manufacturerServiceMock = new Mock<IManufacturerService>();
        _sharedDatafeedApiClientMock = new Mock<ISharedDatafeedApiClient>();
        // Mock shared datafeed API client for image fetching
        _sharedDatafeedApiClientMock
            .Setup(x => x.FetchProductSourceImagesAsync(It.IsAny<uint>()))
            .ReturnsAsync(Result.Success(new ProductSourceImagesSanitised(1, new ProductImages[] 
            {
                new ProductImages("test-image-url", Convert.ToBase64String(new byte[] { 1, 2, 3, 4 }))
            })));

        _sharedDatafeedApiClientMock
            .Setup(x => x.FetchPriceBookProductImagesAsync(It.IsAny<uint>()))
            .ReturnsAsync(Result.Success(new PriceBookProductImagesSanitised(1, new ProductImages[] 
            {
                new ProductImages("test-image-url", Convert.ToBase64String(new byte[] { 1, 2, 3, 4 }))
            })));

        _taxCategoryServiceMock
            .Setup(x => x.GetAllTaxCategoriesAsync())
            .ReturnsAsync(new List<TaxCategory> { new() { Id = 1, Name = "GST" } });

        _categoryServiceMock
            .Setup(x => x.GetAllCategoriesAsync(It.IsAny<int>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<Category>());

        _categoryServiceMock
            .Setup(x => x.GetCategoryByIdAsync(It.IsAny<int>()))
            .ReturnsAsync(new Category { Id = 1, Name = "Test Category" });

        _categoryServiceMock
            .Setup(x => x.GetProductCategoriesByProductIdAsync(It.IsAny<int>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<ProductCategory>());

        _categoryServiceMock
            .Setup(x => x.InsertProductCategoryAsync(It.IsAny<ProductCategory>()))
            .Returns(Task.CompletedTask);

        _manufacturerServiceMock
            .Setup(x => x.GetAllManufacturersAsync(
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<bool>(),
                It.IsAny<bool?>()))
            .ReturnsAsync(new PagedList<Manufacturer>(new List<Manufacturer>(), 0, 10));

        _manufacturerServiceMock
            .Setup(x => x.GetProductManufacturersByProductIdAsync(It.IsAny<int>(), It.IsAny<bool>()))
            .ReturnsAsync(new List<ProductManufacturer>());

        _manufacturerServiceMock
            .Setup(x => x.DeleteProductManufacturerAsync(It.IsAny<ProductManufacturer>()))
            .Returns(Task.CompletedTask);

        _manufacturerServiceMock
            .Setup(x => x.InsertProductManufacturerAsync(It.IsAny<ProductManufacturer>()))
            .Returns(Task.CompletedTask);

        _productAttributeServiceMock
            .Setup(x => x.GetAllProductAttributesAsync(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(new PagedList<ProductAttribute>(new List<ProductAttribute>(), 0, 10));

        _specificationAttributeServiceMock
            .Setup(x => x.GetSpecificationAttributeGroupsAsync(
                It.IsAny<int>(), // pageIndex
                It.IsAny<int>())) // pageSize
            .ReturnsAsync(new PagedList<SpecificationAttributeGroup>(new List<SpecificationAttributeGroup>(), 0, 10));

        _specificationAttributeServiceMock
            .Setup(x => x.GetProductSpecificationAttributesAsync(
                It.IsAny<int>(), // productId
                It.IsAny<int>(), // specificationAttributeOptionId  
                It.IsAny<bool?>(), // allowFiltering
                It.IsAny<bool?>(), // showOnProductPage
                It.IsAny<int?>())) // specificationAttributeGroupId
            .ReturnsAsync(new List<ProductSpecificationAttribute>());

        _specificationAttributeServiceMock
            .Setup(x => x.DeleteProductSpecificationAttributeAsync(It.IsAny<ProductSpecificationAttribute>()))
            .Returns(Task.CompletedTask);

        _specificationAttributeServiceMock
            .Setup(x => x.InsertProductSpecificationAttributeAsync(It.IsAny<ProductSpecificationAttribute>()))
            .Returns(Task.CompletedTask);

        _specificationAttributeServiceMock
            .Setup(x => x.GetSpecificationAttributesByGroupIdAsync(It.IsAny<int?>()))
            .ReturnsAsync(new List<SpecificationAttribute>());

        _specificationAttributeServiceMock
            .Setup(x => x.GetSpecificationAttributeOptionsBySpecificationAttributeAsync(It.IsAny<int>()))
            .ReturnsAsync(new List<SpecificationAttributeOption>());

        _specificationAttributeServiceMock
            .Setup(x => x.InsertSpecificationAttributeOptionAsync(It.IsAny<SpecificationAttributeOption>()))
            .Returns(Task.CompletedTask);

        _pictureServiceMock
            .Setup(x => x.GetPicturesByProductIdAsync(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(new List<Picture>());

        _pictureServiceMock
            .Setup(x => x.InsertPictureAsync(
                It.IsAny<byte[]>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<bool>(),
                It.IsAny<bool>()))
            .ReturnsAsync(new Picture { Id = 1 });

        _customerActivityServiceMock
            .Setup(x => x.InsertActivityAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<BaseEntity>()))
            .ReturnsAsync(new ActivityLog());

        _customerActivityServiceMock
            .Setup(x => x.InsertActivityAsync(It.IsAny<Customer>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<BaseEntity>()))
            .ReturnsAsync(new ActivityLog());

        _urlRecordServiceMock
            .Setup(x => x.InsertUrlRecordAsync(It.IsAny<UrlRecord>()))
            .Returns(Task.CompletedTask);

        _urlRecordServiceMock
            .Setup(x => x.ValidateSeNameAsync(It.IsAny<Product>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<bool>()))
            .ReturnsAsync("test-seo-name");

        _localizationServiceMock
            .Setup(x => x.GetResourceAsync(It.IsAny<string>()))
            .ReturnsAsync((string key) => key);

        SetupProductServiceMock();

        _sut = new ProductSyncService(
            _productServiceMock.Object,
            _pictureServiceMock.Object,
            _categoryServiceMock.Object,
            _localizationServiceMock.Object,
            _customerActivityServiceMock.Object,
            _urlRecordServiceMock.Object,
            _productAttributeServiceMock.Object,
            _specificationAttributeServiceMock.Object,
            _taxCategoryServiceMock.Object,
            _manufacturerServiceMock.Object,
            _sharedDatafeedApiClientMock.Object);
    }

    [TearDown]
    public void TearDown()
    {
        _testProductsBySku.Clear();
        _testProductsById.Clear();
    }

    private void SetupProductServiceMock()
    {
        _productServiceMock
            .Setup(x => x.GetProductBySkuAsync(It.IsAny<string>()))
            .ReturnsAsync((string sku) => _testProductsBySku.GetValueOrDefault(sku));

        _productServiceMock
            .Setup(x => x.GetProductByIdAsync(It.IsAny<int>()))
            .ReturnsAsync((int id) => _testProductsById.GetValueOrDefault(id));

        _productServiceMock
            .Setup(x => x.InsertProductAsync(It.IsAny<Product>()))
            .Callback<Product>((product) =>
            {
                product.Id = _nextProductId++;
                _testProductsBySku[product.Sku] = product;
                _testProductsById[product.Id] = product;
            })
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.UpdateProductAsync(It.IsAny<Product>()))
            .Callback<Product>((product) =>
            {
                if (!_testProductsById.ContainsKey(product.Id))
                {
                    return;
                }

                _testProductsBySku[product.Sku] = product;
                _testProductsById[product.Id] = product;
            })
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.DeleteProductAsync(It.IsAny<Product>()))
            .Callback<Product>((product) =>
            {
                _testProductsBySku.Remove(product.Sku);
                _testProductsById.Remove(product.Id);
            })
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.DeleteProductsAsync(It.IsAny<IList<Product>>()))
            .Callback<IList<Product>>((products) =>
            {
                foreach (var product in products)
                {
                    _testProductsBySku.Remove(product.Sku);
                    _testProductsById.Remove(product.Id);
                }
            })
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.AddStockQuantityHistoryEntryAsync(
                It.IsAny<Product>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<int?>()))
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.InsertProductPictureAsync(It.IsAny<ProductPicture>()))
            .Returns(Task.CompletedTask);

        _productServiceMock
            .Setup(x => x.SearchProductsAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<IList<int>>(),
                It.IsAny<IList<int>>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<ProductType?>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<decimal?>(),
                It.IsAny<decimal?>(),
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<bool>(),
                It.IsAny<int>(),
                It.IsAny<IList<SpecificationAttributeOption>>(),
                It.IsAny<ProductSortingEnum>(),
                It.IsAny<bool>(),
                It.IsAny<bool?>()))
            .Returns((int pageIndex, int pageSize, IList<int> categoryIds,
                IList<int> manufacturerIds, int storeId, int vendorId, int warehouseId,
                ProductType? productType, bool visibleIndividuallyOnly, bool excludeFeaturedProducts,
                decimal? priceMin, decimal? priceMax, int productTagId, string keywords,
                bool searchDescriptions, bool searchManufacturerPartNumber, bool searchSku,
                bool searchProductTags, int languageId, IList<SpecificationAttributeOption> filteredSpecOptions,
                ProductSortingEnum orderBy, bool showHidden, bool? overridePublished) =>
            {
                var products = _testProductsById.Values.ToList();
                var pagedProducts = products
                    .Skip(pageIndex * pageSize)
                    .Take(pageSize)
                    .ToList();

                return Task.FromResult<IPagedList<Product>>(new PagedList<Product>(pagedProducts, pageIndex, pageSize,
                    products.Count));
            });
    }

    private void SeedTestProducts(params (string sku, string name)[] products)
    {
        foreach (var (sku, name) in products)
        {
            var product = new Product
            {
                Id = _nextProductId++,
                Sku = sku,
                Name = name,
                Published = true,
                CreatedOnUtc = DateTime.UtcNow,
                UpdatedOnUtc = DateTime.UtcNow
            };
            _testProductsBySku[sku] = product;
            _testProductsById[product.Id] = product;
        }
    }

    private void SeedLargeProductCatalogue(int totalProducts, int feedCount)
    {
        var feedSkus = new HashSet<string>();
        for (var i = 1; i <= feedCount; i++)
        {
            feedSkus.Add($"FEED_SKU_{i:D6}");
        }

        for (var i = 1; i <= totalProducts; i++)
        {
            var sku = i <= feedCount
                ? $"FEED_SKU_{i:D6}"
                : $"LEGACY_SKU_{i:D6}";

            var product = new Product
            {
                Id = _nextProductId++,
                Sku = sku,
                Name = $"Product {i}",
                Published = true,
                CreatedOnUtc = DateTime.UtcNow,
                UpdatedOnUtc = DateTime.UtcNow
            };
            
            _testProductsBySku[sku] = product;
            _testProductsById[product.Id] = product;
        }
    }

    [Test]
    public async Task SyncProductsAsync_RemovesProductsNotInFeed()
    {
        // Arrange: Seed test database with products (SKU001, SKU002, SKU003), prepare feed records with only SKU001
        SeedTestProducts(
            ("SKU001", "Product 1"),
            ("SKU002", "Product 2"),
            ("SKU003", "Product 3")
        );

        var feedRecords = new[]
        {
            new DatafeedCsvFeedModel(
                Sku: "SKU001",
                VisibleIndividually: true,
                Name: "Product 1 Updated",
                ShortDescription: "Short desc",
                LongDescription: "Long desc",
                ShowOnHomePage: false,
                QtyAvailable: 10,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 20.00m,
                Published: true,
                NopCategoryId: 0,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "TestBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 10.00m,
                Archived: false)
        };

        // Act: Call SyncProductsAsync with the feed records
        var result = await _sut.SyncProductsAsync(feedRecords);

        // Assert: GetProductBySkuAsync returns null for SKU002 and SKU003, returns product for SKU001
        result.IsSuccess.Should().BeTrue();

        var product1 = await _productServiceMock.Object.GetProductBySkuAsync("SKU001");
        product1.Should().NotBeNull();
        product1.Name.Should().Be("Product 1 Updated");

        var product2 = await _productServiceMock.Object.GetProductBySkuAsync("SKU002");
        product2.Should().BeNull("Product with SKU002 should be removed as it's not in the feed");

        var product3 = await _productServiceMock.Object.GetProductBySkuAsync("SKU003");
        product3.Should().BeNull("Product with SKU003 should be removed as it's not in the feed");
    }

    [Test]
    public async Task SyncProductsAsync_HandlesEmptyFeed()
    {
        // Arrange: Seed test database with multiple products, prepare empty feed records array
        SeedTestProducts(
            ("SKU001", "Product 1"),
            ("SKU002", "Product 2"),
            ("SKU003", "Product 3"),
            ("SKU004", "Product 4"),
            ("SKU005", "Product 5")
        );

        var feedRecords = Array.Empty<DatafeedCsvFeedModel>();

        // Act: Call SyncProductsAsync with empty records
        var result = await _sut.SyncProductsAsync(feedRecords);

        // Assert: GetProductBySkuAsync returns null for all previously existing products
        result.IsSuccess.Should().BeTrue();

        var product1 = await _productServiceMock.Object.GetProductBySkuAsync("SKU001");
        product1.Should().BeNull("Product with SKU001 should be removed when feed is empty");

        var product2 = await _productServiceMock.Object.GetProductBySkuAsync("SKU002");
        product2.Should().BeNull("Product with SKU002 should be removed when feed is empty");

        var product3 = await _productServiceMock.Object.GetProductBySkuAsync("SKU003");
        product3.Should().BeNull("Product with SKU003 should be removed when feed is empty");

        var product4 = await _productServiceMock.Object.GetProductBySkuAsync("SKU004");
        product4.Should().BeNull("Product with SKU004 should be removed when feed is empty");

        var product5 = await _productServiceMock.Object.GetProductBySkuAsync("SKU005");
        product5.Should().BeNull("Product with SKU005 should be removed when feed is empty");
    }

    [Test]
    public async Task SyncProductsAsync_ProcessesLargeProductCatalogues()
    {
        // Arrange: Seed test database with 13,000+ products, prepare feed with subset of 100 SKUs
        const int totalProducts = 13000;
        const int feedCount = 100;
        SeedLargeProductCatalogue(totalProducts, feedCount);

        var feedRecords = new List<DatafeedCsvFeedModel>();

        // Create feed records for the 100 products that should remain
        for (var i = 1; i <= feedCount; i++)
        {
            var sku = $"FEED_SKU_{i:D6}";
            feedRecords.Add(new DatafeedCsvFeedModel(
                Sku: sku,
                VisibleIndividually: true,
                Name: $"Product {i}",
                ShortDescription: "Short desc",
                LongDescription: "Long desc",
                ShowOnHomePage: false,
                QtyAvailable: 10,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 20.00m,
                Published: true,
                NopCategoryId: 1,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "TestBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 10.00m,
                Archived: false));
        }

        // Act: Call SyncProductsAsync and let it complete
        var result = await _sut.SyncProductsAsync(feedRecords.ToArray());

        // Assert: Only the 100 products in feed remain retrievable, operation returns Success
        result.IsSuccess.Should().BeTrue("Synchronization should complete successfully for large catalog");

        _testProductsBySku.Count.Should().Be(feedCount, "Only the 100 products from the feed should remain");

        // Verify that feed products remain
        for (var i = 1; i <= feedCount; i++)
        {
            var sku = $"FEED_SKU_{i:D6}";
            var product = await _productServiceMock.Object.GetProductBySkuAsync(sku);
            product.Should().NotBeNull($"Product with SKU {sku} should exist");
        }

        // Spot check some legacy products should be removed
        var legacyProduct = await _productServiceMock.Object.GetProductBySkuAsync("LEGACY_SKU_000012");
        legacyProduct.Should().BeNull("Legacy product should be removed");
    }

    [Test]
    public async Task SyncProductsAsync_CreatesAndUpdatesProductsWhileRemovingOthers()
    {
        // Arrange: Seed with existing products, prepare feed with mix of new, existing, and missing products
        SeedTestProducts(
            ("EXISTING_SKU_001", "Existing Product 1"),
            ("EXISTING_SKU_002", "Existing Product 2"),
            ("TO_BE_REMOVED_001", "Product to Remove 1"),
            ("TO_BE_REMOVED_002", "Product to Remove 2")
        );

        var feedRecords = new[]
        {
            // Existing product to be updated
            new DatafeedCsvFeedModel(
                Sku: "EXISTING_SKU_001",
                VisibleIndividually: true,
                Name: "Updated Product 1",
                ShortDescription: "Updated short desc",
                LongDescription: "Updated long desc",
                ShowOnHomePage: false,
                QtyAvailable: 20,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 25.00m,
                Published: true,
                NopCategoryId: 1,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "UpdatedBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 15.00m,
                Archived: false),
            // Existing product to remain unchanged
            new DatafeedCsvFeedModel(
                Sku: "EXISTING_SKU_002",
                VisibleIndividually: true,
                Name: "Existing Product 2",
                ShortDescription: "Short desc",
                LongDescription: "Long desc",
                ShowOnHomePage: false,
                QtyAvailable: 10,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 20.00m,
                Published: true,
                NopCategoryId: 1,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "TestBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 10.00m,
                Archived: false),
            // New product to be created
            new DatafeedCsvFeedModel(
                Sku: "NEW_SKU_001",
                VisibleIndividually: true,
                Name: "New Product 1",
                ShortDescription: "New short desc",
                LongDescription: "New long desc",
                ShowOnHomePage: false,
                QtyAvailable: 15,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 30.00m,
                Published: true,
                NopCategoryId: 1,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "NewBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 20.00m,
                Archived: false)
        };

        // Act: Call SyncProductsAsync
        var result = await _sut.SyncProductsAsync(feedRecords);

        // Assert: New products are retrievable, existing products are updated, missing products return null
        result.IsSuccess.Should().BeTrue("Synchronisation should complete successfully");

        // Check updated existing product
        var updatedProduct = await _productServiceMock.Object.GetProductBySkuAsync("EXISTING_SKU_001");
        updatedProduct.Should().NotBeNull("Updated existing product should exist");
        updatedProduct.Name.Should().Be("Updated Product 1", "Product name should be updated");
        updatedProduct.Price.Should().Be(25.00m, "Product price should be updated");

        // Check unchanged existing product
        var unchangedProduct = await _productServiceMock.Object.GetProductBySkuAsync("EXISTING_SKU_002");
        unchangedProduct.Should().NotBeNull("Unchanged existing product should exist");
        unchangedProduct.Name.Should().Be("Existing Product 2", "Product name should remain unchanged");

        // Check new product
        var newProduct = await _productServiceMock.Object.GetProductBySkuAsync("NEW_SKU_001");
        newProduct.Should().NotBeNull("New product should be created");
        newProduct.Name.Should().Be("New Product 1", "New product should have correct name");

        // Check removed products
        var removedProduct1 = await _productServiceMock.Object.GetProductBySkuAsync("TO_BE_REMOVED_001");
        removedProduct1.Should().BeNull("Product not in feed should be removed");

        var removedProduct2 = await _productServiceMock.Object.GetProductBySkuAsync("TO_BE_REMOVED_002");
        removedProduct2.Should().BeNull("Product not in feed should be removed");
    }
    
    [Test]
    public async Task SyncProductsAsync_ReturnsFailureWhenCancelled()
    {
        // Arrange: Large dataset, cancellation token that cancels mid-operation
        const int totalProducts = 1000;
        const int feedCount = 50;
        SeedLargeProductCatalogue(totalProducts, feedCount);

        var feedRecords = new List<DatafeedCsvFeedModel>();
        
        // Create many feed records to simulate long-running operation
        for (var i = 1; i <= feedCount; i++)
        {
            var sku = $"FEED_SKU_{i:D6}";
            feedRecords.Add(new DatafeedCsvFeedModel(
                Sku: sku,
                VisibleIndividually: true,
                Name: $"Product {i}",
                ShortDescription: "Short desc",
                LongDescription: "Long desc",
                ShowOnHomePage: false,
                QtyAvailable: 10,
                DisplayStockQuantity: true,
                DisplayStockAvailability: true,
                OrderMinimumQuantity: 1,
                OrderMaximumQuantity: 100,
                SellPriceInc: 20.00m,
                Published: true,
                NopCategoryId: 1,
                JsonFeatures: "[]",
                JsonSpecifications: "[]",
                Brand: "TestBrand",
                ProductSourceId: 1,
                PriceBookItemId: 1,
                CostPrice: 10.00m,
                Archived: false));
        }

        // Create a cancellation token source and cancel it immediately
        var cancellationTokenSource = new CancellationTokenSource();
        await cancellationTokenSource.CancelAsync();

        // Act: Call SyncProductsAsync with cancelling token
        var result = await _sut.SyncProductsAsync(feedRecords.ToArray(), cancellationTokenSource.Token);

        // Assert: Result.IsFailure is true with cancellation message
        result.IsFailure.Should().BeTrue("Operation should fail when cancelled");
        result.Error.Should().Be("Product synchronisation was cancelled", "Failure message should indicate cancellation");
    }
}