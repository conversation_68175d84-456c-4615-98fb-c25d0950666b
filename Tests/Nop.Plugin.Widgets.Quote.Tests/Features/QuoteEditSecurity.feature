@QuoteEditSecurity
Feature: Quote Edit Data Security
    As a system administrator
    I want to ensure that sensitive cost data is never exposed to customers
    So that buy prices, profit margins, and cost information remain confidential

    Background:
        Given the quote editing system is configured
        And there is a quote with complete CRM data including:
            | Field               | Value      |
            | QuoteId            | 12345      |
            | AccountId          | 1379       |
            | UnitBuyPrice       | 100.00     |
            | UnitSellPrice      | 150.00     |
            | BuyPriceSubtotal   | 500.00     |
            | SellPriceSubtotal  | 750.00     |
            | BuyPriceTotal      | 500.00     |
            | SellPriceTotal     | 750.00     |
            | GrossProfit        | 250.00     |
            | MarginPercent      | 33.33      |

    Scenario: Quote details view excludes sensitive cost data
        Given an authenticated school administrator for account 1379
        When they view the quote edit page for quote 12345
        Then the response should contain sell price data:
            | Field               | Value      |
            | UnitSellPrice      | 150.00     |
            | SellPriceSubtotal  | 750.00     |
            | SellPriceTotal     | 750.00     |
        And the response should NOT contain buy price data:
            | Field               |
            | UnitBuyPrice       |
            | BuyPriceSubtotal   |
            | BuyPriceTotal      |
            | GrossProfit        |
            | MarginPercent      |

    Scenario: AJAX quantity update response excludes sensitive cost data
        Given an authenticated school administrator for account 1379
        And they are on the quote edit page for quote 12345
        When they update a line item quantity via AJAX
        Then the AJAX response should contain only customer-safe data:
            | Field               | Present |
            | SellPriceTotal     | Yes     |
            | SellPriceGST       | Yes     |
            | SellPriceInc       | Yes     |
            | UnitSellPrice      | Yes     |
            | SellPriceSubtotal  | Yes     |
        And the AJAX response should NOT contain sensitive data:
            | Field               | Present |
            | UnitBuyPrice       | No      |
            | BuyPriceSubtotal   | No      |
            | BuyPriceTotal      | No      |
            | GrossProfit        | No      |
            | MarginPercent      | No      |

    Scenario: AJAX line item removal response excludes sensitive cost data
        Given an authenticated school administrator for account 1379
        And they are on the quote edit page for quote 12345
        When they remove a line item via AJAX
        Then the AJAX response should contain only customer-safe totals
        And the AJAX response should NOT contain any buy price or profit data

    Scenario: Data transformation service filters sensitive data
        Given a CRM API response containing complete quote data with sensitive information
        When the data transformation service processes the response
        Then the resulting view model should contain only customer-safe data
        And all buy prices should be filtered out
        And all profit and margin data should be filtered out
        And only sell prices and customer-appropriate fields should remain

    Scenario: CRM API client responses are not directly serialised to frontend
        Given the CRM API returns complete quote data including sensitive information
        When a quote edit request is processed
        Then the CRM response should be transformed before frontend serialisation
        And no CRM API response should be directly returned to the client
        And all frontend responses should use customer-safe view models only

    Scenario: Browser developer tools cannot access sensitive data
        Given an authenticated school administrator viewing a quote edit page
        When they inspect the page source and network requests
        Then no buy prices should be visible in the HTML
        And no profit data should be visible in JavaScript variables
        And no sensitive data should be present in AJAX response payloads
        And only sell prices should be accessible via browser tools

    Scenario: JSON serialisation excludes sensitive properties
        Given a quote edit view model is being serialised for frontend consumption
        When the serialisation process occurs
        Then the JSON output should not contain buy price properties
        And the JSON output should not contain profit or margin properties
        And only customer-safe properties should be included in the serialised output

    Scenario: Server-side filtering prevents data leakage
        Given the quote editing system receives CRM data with sensitive information
        When any response is prepared for the frontend
        Then server-side filtering should remove all sensitive data
        And the filtering should occur before any serialisation
        And no sensitive data should ever reach the client-side code
