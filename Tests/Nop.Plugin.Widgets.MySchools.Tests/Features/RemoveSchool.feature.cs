// ------------------------------------------------------------------------------
//  <auto-generated>
//      This code was generated by Reqnroll (https://www.reqnroll.net/).
//      Reqnroll Version:*******
//      Reqnroll Generator Version:*******
// 
//      Changes to this file may cause incorrect behavior and will be lost if
//      the code is regenerated.
//  </auto-generated>
// ------------------------------------------------------------------------------
#region Designer generated code
#pragma warning disable
using Reqnroll;
namespace Nop.Plugin.Widgets.MySchools.Tests.Features
{
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Reqnroll", "*******")]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [NUnit.Framework.TestFixtureAttribute()]
    [NUnit.Framework.DescriptionAttribute("Ability for a user to remove their association with a school")]
    [NUnit.Framework.FixtureLifeCycleAttribute(NUnit.Framework.LifeCycle.InstancePerTestCase)]
    [NUnit.Framework.CategoryAttribute("RemoveSchool")]
    public partial class AbilityForAUserToRemoveTheirAssociationWithASchoolFeature
    {
        
        private global::Reqnroll.ITestRunner testRunner;
        
        private static string[] featureTags = new string[] {
                "RemoveSchool"};
        
        private static global::Reqnroll.FeatureInfo featureInfo = new global::Reqnroll.FeatureInfo(new global::System.Globalization.CultureInfo("en-US"), "Features", "Ability for a user to remove their association with a school", null, global::Reqnroll.ProgrammingLanguage.CSharp, featureTags);
        
#line 1 "RemoveSchool.feature"
#line hidden
        
        [NUnit.Framework.OneTimeSetUpAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureSetupAsync()
        {
        }
        
        [NUnit.Framework.OneTimeTearDownAttribute()]
        public static async global::System.Threading.Tasks.Task FeatureTearDownAsync()
        {
        }
        
        [NUnit.Framework.SetUpAttribute()]
        public async global::System.Threading.Tasks.Task TestInitializeAsync()
        {
            testRunner = global::Reqnroll.TestRunnerManager.GetTestRunnerForAssembly(featureHint: featureInfo);
            try
            {
                if (((testRunner.FeatureContext != null) 
                            && (testRunner.FeatureContext.FeatureInfo.Equals(featureInfo) == false)))
                {
                    await testRunner.OnFeatureEndAsync();
                }
            }
            finally
            {
                if (((testRunner.FeatureContext != null) 
                            && testRunner.FeatureContext.BeforeFeatureHookFailed))
                {
                    throw new global::Reqnroll.ReqnrollException("Scenario skipped because of previous before feature hook error");
                }
                if ((testRunner.FeatureContext == null))
                {
                    await testRunner.OnFeatureStartAsync(featureInfo);
                }
            }
        }
        
        [NUnit.Framework.TearDownAttribute()]
        public async global::System.Threading.Tasks.Task TestTearDownAsync()
        {
            if ((testRunner == null))
            {
                return;
            }
            try
            {
                await testRunner.OnScenarioEndAsync();
            }
            finally
            {
                global::Reqnroll.TestRunnerManager.ReleaseTestRunner(testRunner);
                testRunner = null;
            }
        }
        
        public void ScenarioInitialize(global::Reqnroll.ScenarioInfo scenarioInfo)
        {
            testRunner.OnScenarioInitialize(scenarioInfo);
            testRunner.ScenarioContext.ScenarioContainer.RegisterInstanceAs<NUnit.Framework.TestContext>(NUnit.Framework.TestContext.CurrentContext);
        }
        
        public async global::System.Threading.Tasks.Task ScenarioStartAsync()
        {
            await testRunner.OnScenarioStartAsync();
        }
        
        public async global::System.Threading.Tasks.Task ScenarioCleanupAsync()
        {
            await testRunner.CollectScenarioErrorsAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("A malicious attacker tries to access the Remove School endpoint directly")]
        [NUnit.Framework.TestCaseAttribute("not logged in", null)]
        [NUnit.Framework.TestCaseAttribute("a guest", null)]
        public async global::System.Threading.Tasks.Task AMaliciousAttackerTriesToAccessTheRemoveSchoolEndpointDirectly(string role, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("role", role);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("A malicious attacker tries to access the Remove School endpoint directly", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 4
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 5
        await testRunner.GivenAsync(string.Format("a malicious attacker who is \'{0}\'", role), ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 6
        await testRunner.WhenAsync("they access the Remove School endpoint directly", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 7
        await testRunner.ThenAsync("they should be redirected to the Login page", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("The logged in customer tries to remove an association with a school whose index i" +
            "s out of bounds")]
        public async global::System.Threading.Tasks.Task TheLoggedInCustomerTriesToRemoveAnAssociationWithASchoolWhoseIndexIsOutOfBounds()
        {
            string[] tagsOfScenario = ((string[])(null));
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("The logged in customer tries to remove an association with a school whose index i" +
                    "s out of bounds", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 14
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 15
        await testRunner.GivenAsync("a logged in customer on the My Schools page", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 16
        await testRunner.WhenAsync("they access the Remove School endpoint directly", ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 17
        await testRunner.ThenAsync("the page should be refreshed", ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 18
        await testRunner.AndAsync("an error notification with message \'Invalid school\' should be displayed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
        
        [NUnit.Framework.TestAttribute()]
        [NUnit.Framework.DescriptionAttribute("The logged in customer wants to remove their association with <schoolIndex>")]
        [NUnit.Framework.TestCaseAttribute("1", "Your association with this school has been successfully removed.", null)]
        public async global::System.Threading.Tasks.Task TheLoggedInCustomerWantsToRemoveTheirAssociationWithSchoolIndex(string schoolIndex, string successMessage, string[] exampleTags)
        {
            string[] tagsOfScenario = exampleTags;
            global::System.Collections.Specialized.OrderedDictionary argumentsOfScenario = new global::System.Collections.Specialized.OrderedDictionary();
            argumentsOfScenario.Add("schoolIndex", schoolIndex);
            argumentsOfScenario.Add("successMessage", successMessage);
            global::Reqnroll.ScenarioInfo scenarioInfo = new global::Reqnroll.ScenarioInfo("The logged in customer wants to remove their association with <schoolIndex>", null, tagsOfScenario, argumentsOfScenario, featureTags);
#line 20
    this.ScenarioInitialize(scenarioInfo);
#line hidden
            if ((global::Reqnroll.TagHelper.ContainsIgnoreTag(scenarioInfo.CombinedTags) || global::Reqnroll.TagHelper.ContainsIgnoreTag(featureTags)))
            {
                testRunner.SkipScenario();
            }
            else
            {
                await this.ScenarioStartAsync();
#line 21
        await testRunner.GivenAsync("a logged in customer on the My Schools page", ((string)(null)), ((global::Reqnroll.Table)(null)), "Given ");
#line hidden
#line 22
        await testRunner.WhenAsync(string.Format("they click the Remove button on the school {0} card", schoolIndex), ((string)(null)), ((global::Reqnroll.Table)(null)), "When ");
#line hidden
#line 23
        await testRunner.ThenAsync(string.Format("their association with {0} should be removed", schoolIndex), ((string)(null)), ((global::Reqnroll.Table)(null)), "Then ");
#line hidden
#line 24
        await testRunner.AndAsync("the page should be refreshed", ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
#line 25
        await testRunner.AndAsync(string.Format("a success notification with message \'{0}\' should be displayed", successMessage), ((string)(null)), ((global::Reqnroll.Table)(null)), "And ");
#line hidden
            }
            await this.ScenarioCleanupAsync();
        }
    }
}
#pragma warning restore
#endregion
