@inherits Nop.Web.Framework.Mvc.Razor.NopRazorPage<TModel>

@inject INopHtmlHelper NopHtml

@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@*we remove the default InputTagHelper to prevent the checkbox duplicating: https://stackoverflow.com/questions/42544961/asp-net-core-custom-input-tag-helper-rendering-duplicate-checkboxes*@
@removeTagHelper Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper, Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, Nop.Web.Framework
@addTagHelper *, MiniProfiler.AspNetCore.Mvc

@using System.Globalization;
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Mvc.ViewFeatures
@using Microsoft.Extensions.Primitives
@using Nop.Web.Components
@using Nop.Web.Extensions
@using Nop.Web.Framework
@using Nop.Web.Framework.Events
@using Nop.Web.Framework.Extensions
@using Nop.Web.Framework.Infrastructure
@using Nop.Web.Framework.Models
@using Nop.Web.Framework.Mvc.Routing
@using Nop.Web.Framework.Security.Captcha
@using Nop.Web.Framework.Security.Honeypot
@using Nop.Web.Framework.Themes
@using Nop.Web.Framework.UI
@using Nop.Web.Models.Blogs
@using Nop.Web.Models.Boards
@using Nop.Web.Models.Catalog
@using Nop.Web.Models.Checkout
@using Nop.Web.Models.Cms
@using Nop.Web.Models.Common
@using Nop.Web.Models.Customer
@using Nop.Web.Models.Media
@using Nop.Web.Models.News
@using Nop.Web.Models.Newsletter
@using Nop.Web.Models.Order
@using Nop.Web.Models.Polls
@using Nop.Web.Models.PrivateMessages
@using Nop.Web.Models.Profile
@using Nop.Web.Models.ShoppingCart
@using Nop.Web.Models.Sitemap
@using Nop.Web.Models.Topics
@using Nop.Web.Models.Vendors