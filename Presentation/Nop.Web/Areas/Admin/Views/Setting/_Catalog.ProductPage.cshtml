@model CatalogSettingsModel

<div class="card-body">
    <div class="form-group row">
        <div class="col-md-3">
            <nop-override-store-checkbox asp-for="ProductUrlStructureTypeId_OverrideForStore" asp-input="ProductUrlStructureTypeId" asp-store-scope="@Model.ActiveStoreScopeConfiguration" />
            <nop-label asp-for="ProductUrlStructureTypeId" />
        </div>
        <div class="col-md-9">
            <nop-select asp-for="ProductUrlStructureTypeId" asp-items="Model.ProductUrlStructureTypes" />
            <span asp-validation-for="ProductUrlStructureTypeId"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-override-store-checkbox asp-for="ShowFreeShippingNotification_OverrideForStore" asp-input="ShowFreeShippingNotification" asp-store-scope="@Model.ActiveStoreScopeConfiguration" />
            <nop-label asp-for="ShowFreeShippingNotification" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="ShowFreeShippingNotification" />
            <span asp-validation-for="ShowFreeShippingNotification"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-override-store-checkbox asp-for="AllowViewUnpublishedProductPage_OverrideForStore" asp-input="AllowViewUnpublishedProductPage" asp-store-scope="@Model.ActiveStoreScopeConfiguration" />
            <nop-label asp-for="AllowViewUnpublishedProductPage" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="AllowViewUnpublishedProductPage" />
            <span asp-validation-for="AllowViewUnpublishedProductPage"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-override-store-checkbox asp-for="DisplayDiscontinuedMessageForUnpublishedProducts_OverrideForStore" asp-input="DisplayDiscontinuedMessageForUnpublishedProducts" asp-store-scope="@Model.ActiveStoreScopeConfiguration" />
            <nop-label asp-for="DisplayDiscontinuedMessageForUnpublishedProducts" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="DisplayDiscontinuedMessageForUnpublishedProducts" />
            <span asp-validation-for="DisplayDiscontinuedMessageForUnpublishedProducts"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-override-store-checkbox asp-for="AttributeValueOutOfStockDisplayType_OverrideForStore" asp-input="AttributeValueOutOfStockDisplayType" asp-store-scope="@Model.ActiveStoreScopeConfiguration" />
            <nop-label asp-for="AttributeValueOutOfStockDisplayType" />
        </div>
        <div class="col-md-9">
            <nop-select asp-for="AttributeValueOutOfStockDisplayType" asp-items="Model.AttributeValueOutOfStockDisplayTypes" />
            <span asp-validation-for="AttributeValueOutOfStockDisplayType"></span>
        </div>
    </div>
</div>