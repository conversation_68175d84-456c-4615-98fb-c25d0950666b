@model NewsSettingsModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.Configuration.Settings.News").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("News settings");
}


@{
    const string hideCommonBlockAttributeName = "NewsSettingsPage.HideCommonBlock";
    var customer = await workContext.GetCurrentCustomerAsync();
    var hideCommonBlock = await genericAttributeService.GetAttributeAsync<bool>(customer, hideCommonBlockAttributeName);

    const string hideCommentsBlockAttributeName = "NewsSettingsPage.HideCommentsBlock";
    var hideCommentsBlock = await genericAttributeService.GetAttributeAsync<bool>(customer, hideCommentsBlockAttributeName);
}

<form asp-controller="Setting" asp-action="News" method="post">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Configuration.Settings.News")
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.NewsSettingsButtons, additionalData = Model })
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="form-horizontal">
                @await Component.InvokeAsync(typeof(StoreScopeConfigurationViewComponent))
                @await Component.InvokeAsync(typeof(SettingModeViewComponent))
                <div asp-validation-summary="All"></div>

                <nop-cards id="newsettings-cards">
                    <nop-card asp-name="newsettings-common" asp-icon="fas fa-info" asp-title="@T("Admin.Configuration.Settings.News.BlockTitle.Common")" asp-hide-block-attribute-name="@hideCommonBlockAttributeName" asp-hide="@hideCommonBlock" asp-advanced="false">@await Html.PartialAsync("_News.Common", Model)</nop-card>
                    <nop-card asp-name="newsettings-comments" asp-icon="far fa-comments" asp-title="@T("Admin.Configuration.Settings.News.BlockTitle.NewsComments")" asp-hide-block-attribute-name="@hideCommentsBlockAttributeName" asp-hide="@hideCommentsBlock" asp-advanced="false">@await Html.PartialAsync("_News.Comments", Model)</nop-card>

                    @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.NewsDetailsBlock, additionalData = Model })
                </nop-cards>
            </div>
        </div>
    </section>
</form>