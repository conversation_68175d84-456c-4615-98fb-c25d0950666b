@model GdprConsentModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.Configuration.Settings.Gdpr.Consent.AddNew").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("GDPR settings");
}

<form asp-controller="Setting" asp-action="CreateGdprConsent" method="post">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Configuration.Settings.Gdpr.Consent.AddNew")
            <small>
                <i class="fas fa-arrow-circle-left"></i>
                <a asp-action="Gdpr">@T("Admin.Configuration.Settings.Gdpr.Consent.BackToList")</a>
            </small>
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            <button type="submit" name="save-continue" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.SaveContinue")
            </button>
            @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.GdprConsentDetailsButtons, additionalData = Model })
        </div>
    </div>
    @await Html.PartialAsync("_CreateOrUpdateGdprConsent", Model)
</form>