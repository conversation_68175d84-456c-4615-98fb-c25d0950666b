@model DeleteConfirmationModel

<div class="modal-dialog">
    <div class="modal-content">
        <div class="modal-header">            
            <h4 class="modal-title" id="@(Model.WindowId)-title">@T("Admin.Common.AreYouSure")</h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        </div>
        <form asp-action="@Model.ActionName" asp-controller="@Model.ControllerName" asp-route-id="@Model.Id">
            <div class="form-horizontal">
                <div class="modal-body">
                    @T("Admin.Common.DeleteConfirmation")
                </div>
                <div class="modal-footer">
                    <span class="btn btn-default" data-dismiss="modal">@T("Admin.Common.NoCancel")</span>
                    <button type="submit" class="btn btn-danger float-right">
                        @T("Admin.Common.Delete")
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
