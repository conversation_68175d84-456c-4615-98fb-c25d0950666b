@model CountryModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.Configuration.Countries.EditCountryDetails").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("Countries");
}

<form asp-controller="Country" asp-action="Edit" method="post" id="country-form">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Configuration.Countries.EditCountryDetails") - @Model.Name
            <small>
                <i class="fas fa-arrow-circle-left"></i>
                <a asp-action="List">@T("Admin.Configuration.Countries.BackToList")</a>
            </small>
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            <button type="submit" name="save-continue" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.SaveContinue")
            </button>
            <span id="country-delete" class="btn btn-danger">
                <i class="far fa-trash-alt"></i>
                @T("Admin.Common.Delete")
            </span>
            @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.CountryDetailsButtons, additionalData = Model })
        </div>
    </div>
    @await Html.PartialAsync("_CreateOrUpdate", Model)
</form>
<nop-delete-confirmation asp-model-id="@Model.Id" asp-button-id="country-delete" />