@model OnlineCustomerSearchModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.Customers.OnlineCustomers").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("Online customers");
}

<div class="content-header clearfix">
    <h1 class="float-left">
        @T("Admin.Customers.OnlineCustomers")
    </h1>
    <div class="float-right">
        &nbsp;
        @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.OnlineCustomerListButtons, additionalData = Model })
    </div>
</div>

<section class="content">
    <div class="container-fluid">
    <div class="form-horizontal">
        <div class="cards-group">
            <div class="card card-default">
                <div class="card-body">
                    <nop-doc-reference asp-string-resource="@T("Admin.Documentation.Reference.OnlineCustomers", Docs.OnlineCustomers + Utm.OnAdmin)" />

                    @await Html.PartialAsync("Table", new DataTablesModel
                    {
                        Name = "onlinecustomers-grid",
                        UrlRead = new DataUrl("List", "OnlineCustomer", null),
                        Length = Model.PageSize,
                        LengthMenu = Model.AvailablePageSizes,
                        ColumnCollection = new List<ColumnProperty>
                        {
                            new ColumnProperty(nameof(OnlineCustomerModel.CustomerInfo))
                            {
                                Title = T("Admin.Customers.OnlineCustomers.Fields.CustomerInfo").Text,
                                Width = "100",
                                Render = new RenderLink(new DataUrl("~/Admin/Customer/Edit", nameof(CustomerModel.Id)))
                            },
                            new ColumnProperty(nameof(OnlineCustomerModel.LastIpAddress))
                            {
                                Title = T("Admin.Customers.OnlineCustomers.Fields.IPAddress").Text,
                                Width = "100"
                            },
                            new ColumnProperty(nameof(OnlineCustomerModel.Location))
                            {
                                Title = T("Admin.Customers.OnlineCustomers.Fields.Location").Text,
                                Width = "100"
                            },
                            new ColumnProperty(nameof(OnlineCustomerModel.LastActivityDate))
                            {
                                Title = T("Admin.Customers.OnlineCustomers.Fields.LastActivityDate").Text,
                                Width = "200",
                                Render = new RenderDate()
                            },
                            new ColumnProperty(nameof(OnlineCustomerModel.LastVisitedPage))
                            {
                                Title = T("Admin.Customers.OnlineCustomers.Fields.LastVisitedPage").Text,
                                Width = "100"
                            }
                        }
                    })
                </div>
            </div>
        </div>
    </div>
</div>
</section>