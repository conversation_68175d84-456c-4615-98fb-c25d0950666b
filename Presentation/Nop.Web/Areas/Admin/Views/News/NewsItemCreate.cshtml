@model NewsItemModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.ContentManagement.News.NewsItems.AddNew").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("News items");
}

<form asp-controller="News" asp-action="NewsItemCreate" method="post">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.ContentManagement.News.NewsItems.AddNew")
            <small>
                <i class="fas fa-arrow-circle-left"></i>
                <a asp-action="NewsItems">@T("Admin.ContentManagement.News.NewsItems.BackToList")</a>
            </small>
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            <button type="submit" name="save-continue" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.SaveContinue")
            </button>
            @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.NewsDetailsButtons, additionalData = Model })
        </div>
    </div>
    @await Html.PartialAsync("_CreateOrUpdate", Model)
</form>