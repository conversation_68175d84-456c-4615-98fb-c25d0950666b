@using Nop.Core.Domain.Catalog
@using Nop.Services
@model RecurringPaymentModel


<div class="card-body">
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="InitialOrderId" />
        </div>
        <div class="col-md-9">
            <a asp-controller="Order" asp-action="Edit" asp-route-id="@Model.InitialOrderId">@T("Admin.Common.View")</a>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="CustomerEmail" />
        </div>
        <div class="col-md-9">
            <a asp-controller="Customer" asp-action="Edit" asp-route-id="@Model.CustomerId">@Model.CustomerEmail</a>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="CycleLength" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="CycleLength" />
            <span asp-validation-for="CycleLength"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="CyclePeriodId" />
        </div>
        <div class="col-md-9">
            <nop-select asp-for="CyclePeriodId" asp-items="@await (((RecurringProductCyclePeriod)Model.CyclePeriodId).ToSelectListAsync())" />
            <span asp-validation-for="CyclePeriodId"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="TotalCycles" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="TotalCycles" />
            <span asp-validation-for="TotalCycles"></span>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="CyclesRemaining" />
        </div>
        <div class="col-md-9">
            <div class="form-text-row">@Model.CyclesRemaining</div>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="PaymentType" />
        </div>
        <div class="col-md-9">
            <div class="form-text-row">@Model.PaymentType</div>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="StartDate" />
        </div>
        <div class="col-md-9">
            <div class="form-text-row">@Model.StartDate</div>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-md-3">
            <nop-label asp-for="IsActive" />
        </div>
        <div class="col-md-9">
            <nop-editor asp-for="IsActive" />
            <span asp-validation-for="IsActive"></span>
        </div>
    </div>
</div>
