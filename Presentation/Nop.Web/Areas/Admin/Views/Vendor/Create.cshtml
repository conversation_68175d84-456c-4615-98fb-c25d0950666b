@model VendorModel

@{
    //page title
    ViewBag.PageTitle = T("Admin.Vendors.AddNew").Text;
    //active menu item (system name)
    NopHtml.SetActiveMenuItemSystemName("Vendors");
}

<form asp-controller="Vendor" asp-action="Create" method="post">
    <div class="content-header clearfix">
        <h1 class="float-left">
            @T("Admin.Vendors.AddNew")
            <small>
                <i class="fas fa-arrow-circle-left"></i>
                <a asp-action="List">@T("Admin.Vendors.BackToList")</a>
            </small>
        </h1>
        <div class="float-right">
            <button type="submit" name="save" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.Save")
            </button>
            <button type="submit" name="save-continue" class="btn btn-primary">
                <i class="far fa-save"></i>
                @T("Admin.Common.SaveContinue")
            </button>
            @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.VendorDetailsButtons, additionalData = Model })
        </div>
    </div>
    @await Html.PartialAsync("_CreateOrUpdate", Model)
</form>