@model SpecificationAttributeModel

@if (Model.Id > 0)
{
    <div class="card-body">
        @await Html.PartialAsync("Table", new DataTablesModel
        {
            Name = "specificationattributeoptions-grid",
            UrlRead = new DataUrl("OptionList", "SpecificationAttribute", new RouteValueDictionary { [nameof(Model.SpecificationAttributeOptionSearchModel.SpecificationAttributeId)] = Model.SpecificationAttributeOptionSearchModel.SpecificationAttributeId }),
            UrlDelete = new DataUrl("OptionDelete", "SpecificationAttribute", null),
            Length = Model.SpecificationAttributeOptionSearchModel.PageSize,
            LengthMenu = Model.SpecificationAttributeOptionSearchModel.AvailablePageSizes,
            ColumnCollection = new List<ColumnProperty>
            {
                new ColumnProperty(nameof(SpecificationAttributeOptionModel.Name))
                {
                    Title = T("Admin.Catalog.Attributes.SpecificationAttributes.SpecificationAttribute.Options.Fields.Name").Text
                },
                new ColumnProperty(nameof(SpecificationAttributeOptionModel.DisplayOrder))
                {
                    Title = T("Admin.Catalog.Attributes.SpecificationAttributes.SpecificationAttribute.Options.Fields.DisplayOrder").Text,
                    Width = "100",
                    ClassName =  NopColumnClassDefaults.CenterAll
                },
                new ColumnProperty(nameof(SpecificationAttributeOptionModel.NumberOfAssociatedProducts))
                {
                    Title = T("Admin.Catalog.Attributes.SpecificationAttributes.SpecificationAttribute.Options.Fields.NumberOfAssociatedProducts").Text,
                    Width = "250",
                    ClassName =  NopColumnClassDefaults.CenterAll
                },
                new ColumnProperty(nameof(SpecificationAttributeOptionModel.Id))
                {
                    Title = T("Admin.Common.Edit").Text,
                    Width = "100",
                    ClassName =  NopColumnClassDefaults.Button,
                    Render = new RenderCustom("renderColumnEdit")
                },
                new ColumnProperty(nameof(SpecificationAttributeOptionModel.Id))
                {
                    Title = T("Admin.Common.Delete").Text,
                    Width = "100",
                    Render = new RenderButtonRemove(T("Admin.Common.Delete").Text),
                    ClassName =  NopColumnClassDefaults.Button
                }
            }
        })
    
        <script>
            function renderColumnEdit(data, type, row, meta) {
                return '<button onclick=\"javascript:OpenWindow(\'@Url.Content("~/Admin/SpecificationAttribute/OptionEditPopup/")' + data + '?btnId=btnRefresh&formId=specificationattribute-form\', 800, 400, true); return false;\" class="btn btn-default"><i class="fas fa-pencil-alt"></i>@T("Admin.Common.Edit").Text</button>';
            }
        </script>
    </div>
    
    <div class="card-footer">
        <button type="submit" id="btnAddNewOption" onclick="javascript:OpenWindow('@(Url.Action("OptionCreatePopup", "SpecificationAttribute", new { specificationAttributeId = Model.Id, btnId = "btnRefresh", formId = "specificationattribute-form" }))', 800, 400, true); return false;" class="btn btn-primary">
            @T("Admin.Catalog.Attributes.SpecificationAttributes.SpecificationAttribute.Options.AddNew")
        </button>
        <button type="submit" id="btnRefresh" style="display: none"></button>
        <script>
            $(document).ready(function () {
                $('#btnRefresh').click(function () {
                    //refresh grid
                    updateTable('#specificationattributeoptions-grid');

                    //return false to don't reload a page
                    return false;
                });
            });
        </script>
    </div>
}    
else
{
    <div class="card-body">
        @T("Admin.Catalog.Attributes.SpecificationAttributes.SpecificationAttribute.Options.SaveBeforeEdit")
    </div>
}

