@model TemplatesModel

<div class="card-body">
    @await Component.InvokeAsync(typeof(AdminWidgetViewComponent), new { widgetZone = AdminWidgetZones.ProductTemplateListButtons, additionalData = Model })

    @await Html.PartialAsync("Table", new DataTablesModel
    {
        Name = "templates-product-grid",
        UrlRead = new DataUrl("ProductTemplates", "Template", null),
        UrlUpdate = new DataUrl("ProductTemplateUpdate", "Template", null),
        UrlDelete = new DataUrl("ProductTemplateDelete", "Template", null),

        Length = Model.TemplatesProduct.PageSize,
        LengthMenu = Model.TemplatesProduct.AvailablePageSizes,
        ColumnCollection = new List<ColumnProperty>
        {
            new ColumnProperty(nameof(ProductTemplateModel.Name))
            {
                Title = T("Admin.System.Templates.Product.Name").Text,
                Width = "300",
                Editable = true,
                EditType = EditType.String
            },
            new ColumnProperty(nameof(ProductTemplateModel.ViewPath))
            {
                Title = T("Admin.System.Templates.Product.ViewPath").Text,
                Width = "300",
                Editable = true,
                EditType = EditType.String
            },
            new ColumnProperty(nameof(ProductTemplateModel.DisplayOrder))
            {
                Title = T("Admin.System.Templates.Product.DisplayOrder").Text,
                Width = "100",
                Editable = true,
                EditType = EditType.Number
            },
            new ColumnProperty(nameof(ProductTemplateModel.IgnoredProductTypes))
            {
                Title = T("Admin.System.Templates.Product.IgnoredProductTypes").Text,
                Width = "300",
                Editable = true,
                EditType = EditType.String
            },
            new ColumnProperty(nameof(ProductTemplateModel.Id))
            {
                Title = T("Admin.Common.Edit").Text,
                Width = "200",
                ClassName =  NopColumnClassDefaults.Button,
                Render = new RenderButtonsInlineEdit()
            },
            new ColumnProperty(nameof(ProductTemplateModel.Id))
            {
                Title = T("Admin.Common.Delete").Text,
                Width = "100",
                ClassName =  NopColumnClassDefaults.Button,
                Render = new RenderButtonRemove(T("Admin.Common.Delete").Text)
            }
        }
    })

    <div class="card card-default">
        <div class="card-header">
            @T("Admin.Common.AddNewRecord")
        </div>
        <div class="card-body">
            <div class="form-group row">
                <div class="col-md-3">
                    <nop-label asp-for="@Model.AddProductTemplate.Name" />
                </div>
                <div class="col-md-9">
                    <nop-editor asp-for="@Model.AddProductTemplate.Name" />
                    <span asp-validation-for="@Model.AddProductTemplate.Name"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-3">
                    <nop-label asp-for="@Model.AddProductTemplate.ViewPath" />
                </div>
                <div class="col-md-9">
                    <nop-editor asp-for="@Model.AddProductTemplate.ViewPath" />
                    <span asp-validation-for="@Model.AddProductTemplate.ViewPath"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-3">
                    <nop-label asp-for="@Model.AddProductTemplate.DisplayOrder" />
                </div>
                <div class="col-md-9">
                    <nop-editor asp-for="@Model.AddProductTemplate.DisplayOrder" />
                    <span asp-validation-for="@Model.AddProductTemplate.DisplayOrder"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-3">
                    <nop-label asp-for="@Model.AddProductTemplate.IgnoredProductTypes" />
                </div>
                <div class="col-md-9">
                    <nop-editor asp-for="@Model.AddProductTemplate.IgnoredProductTypes" />
                    <span asp-validation-for="@Model.AddProductTemplate.IgnoredProductTypes"></span>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-9 offset-md-3">
                    <button type="button" id="addProductTemplate" class="btn btn-primary">@T("Admin.Common.AddNewRecord")</button>
                </div>
            </div>
        </div>
        <script>
            $(document).ready(function () {
                $('#addProductTemplate').click(function () {
                    $('#addProductTemplate').attr('disabled', true);
                    var postData = {
                        Name: $("#@Html.IdFor(model => model.AddProductTemplate.Name)").val(),
                        ViewPath: $("#@Html.IdFor(model => model.AddProductTemplate.ViewPath)").val(),
                        DisplayOrder: $("#@Html.IdFor(model => model.AddProductTemplate.DisplayOrder)").val(),
                        IgnoredProductTypes: $("#@Html.IdFor(model => model.AddProductTemplate.IgnoredProductTypes)").val()
                    };
                    addAntiForgeryToken(postData);

                    $.ajax({
                        cache: false,
                        type: "POST",
                        url: "@Html.Raw(Url.Action("ProductTemplateAdd", "Template", null))",
                        data: postData,
                        success: function (data, textStatus, jqXHR) {
                            if (data.Result) {
                                //reload grid
                                updateTable('#templates-product-grid');

                                //clear input value
                                $("#@Html.IdFor(model => model.AddProductTemplate.Name)").val('');
                                $("#@Html.IdFor(model => model.AddProductTemplate.ViewPath)").val('');
                                $("#@Html.IdFor(model => model.AddProductTemplate.DisplayOrder)").data("kendoNumericTextBox").value(0);
                                $("#@Html.IdFor(model => model.AddProductTemplate.IgnoredProductTypes)").val('');

                            } else {
                                //display errors if returned
                                display_nop_error(data);
                            }
                        },
                        complete: function (jqXHR, textStatus) {
                            $('#addProductTemplate').attr('disabled', false);
                        }
                    });
                });
            });
        </script>
    </div>
</div>